/**
 * Data builders for test objects
 * Provides convenient methods to create test data with sensible defaults
 */

export interface ContactRequestData {
  addresseeUserId: string
  message?: string
}

export interface ContactListData {
  name: string
  description?: string
}

export interface GroupData {
  name: string
  description?: string
  visibility?: 'public' | 'private'
  joinApproval?: 'automatic' | 'manual'
}

export interface ProfileData {
  firstName?: string
  lastName?: string
  birthDate?: string
  countryCode?: string
  bio?: string
}

/**
 * Build contact request data with defaults
 */
export function buildContactRequest(addresseeUserId: string, message?: string): ContactRequestData {
  return {
    addresseeUserId,
    message: message || `Hi! I'd like to connect with you.`,
  }
}

/**
 * Build contact list data with defaults
 */
export function buildContactList(name: string, description?: string): ContactListData {
  return {
    name,
    description: description || `Contact list: ${name}`,
  }
}

/**
 * Build group data with defaults
 */
export function buildGroup(name: string, options: Partial<GroupData> = {}): GroupData {
  return {
    name,
    description: options.description || `Test group: ${name}`,
    visibility: options.visibility || 'private',
    joinApproval: options.joinApproval || 'manual',
  }
}

/**
 * Build profile data with defaults
 */
export function buildProfile(overrides: Partial<ProfileData> = {}): ProfileData {
  return {
    firstName: overrides.firstName || 'Test',
    lastName: overrides.lastName || 'User',
    birthDate: overrides.birthDate || '1990-01-01',
    countryCode: overrides.countryCode || 'PHL',
    bio: overrides.bio,
  }
}

/**
 * Generate unique names for testing
 */
export function generateUniqueName(prefix: string): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return `${prefix}_${timestamp}_${random}`
}

/**
 * Generate test email
 */
export function generateTestEmail(prefix: string): string {
  const uniquePart = Date.now() + Math.random().toString(36).substring(2, 8)
  return `${prefix}_${uniquePart}@test.example.com`
}

/**
 * Generate test username
 */
export function generateTestUsername(prefix: string): string {
  const uniquePart = Date.now() + Math.random().toString(36).substring(2, 8)
  return `${prefix}_${uniquePart}`
}

/**
 * Common test data sets
 */
export const TEST_DATA = {
  profiles: {
    developer: buildProfile({
      firstName: 'John',
      lastName: 'Developer',
      bio: 'Full-stack developer with 5+ years experience',
    }),
    designer: buildProfile({
      firstName: 'Jane',
      lastName: 'Designer',
      bio: 'UI/UX designer passionate about user experience',
    }),
    manager: buildProfile({
      firstName: 'Mike',
      lastName: 'Manager',
      bio: 'Project manager with excellent communication skills',
    }),
  },

  groups: {
    development: buildGroup('Development Team', {
      description: 'Core development team for the project',
      visibility: 'private',
      joinApproval: 'manual',
    }),
    design: buildGroup('Design Team', {
      description: 'Creative team responsible for UI/UX',
      visibility: 'private',
      joinApproval: 'automatic',
    }),
    public: buildGroup('Public Discussion', {
      description: 'Open discussion group for all members',
      visibility: 'public',
      joinApproval: 'automatic',
    }),
  },

  contactLists: {
    work: buildContactList('Work Contacts', 'Professional work contacts'),
    personal: buildContactList('Personal Friends', 'Close personal friends and family'),
    colleagues: buildContactList('Colleagues', 'Current and former colleagues'),
  },
}

/**
 * Validation helpers
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function isValidUsername(username: string): boolean {
  // Username should be 3-50 characters, alphanumeric and underscores
  const usernameRegex = /^[a-zA-Z0-9_]{3,50}$/
  return usernameRegex.test(username)
}

export function isValidPassword(password: string): boolean {
  // Password should be at least 8 characters with mixed case and numbers
  return (
    password.length >= 8 && /[a-z]/.test(password) && /[A-Z]/.test(password) && /\d/.test(password)
  )
}
