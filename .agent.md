# SkedAI Backend Development Guide

## Project Overview

**SkedAI** is a comprehensive scheduling and task management backend API built with AdonisJS and TypeScript. The system provides sophisticated privacy controls, activity management, and social features for scheduling applications.

### Technology Stack

- **Framework**: AdonisJS v6 (Node.js TypeScript framework)
- **Language**: TypeScript with strict typing
- **Database**: PostgreSQL with Lucid ORM
- **Authentication**: JWT with refresh tokens
- **Caching**: Redis
- **Testing**: Japa testing framework
- **AI Integration**: OpenAI and Google AI APIs via Vercel AI SDK and LangChain

### Key Features

- User authentication and profile management
- Task and event management with AI assistance
- Sophisticated privacy policy system with template-based rules
- Contact management with lists and groups
- Real-time availability tracking
- Comprehensive API with rate limiting and security

## Project Structure

### Directory Organization

```
app/
├── controllers/          # HTTP controllers (lightweight, HTTP-focused)
├── features/            # Domain-driven feature modules
│   ├── activity/        # Activity management
│   ├── contact/         # Contact and relationship management
│   ├── event/           # Event scheduling
│   ├── privacy-policy/  # Privacy and visibility controls
│   ├── profile/         # User profile management
│   ├── session/         # Authentication and sessions
│   ├── task/            # Task management with AI
│   └── user/            # Core user entities
├── middleware/          # Custom middleware
├── models/              # Base models and shared entities
├── types/               # TypeScript type definitions
├── utils/               # Utility functions and helpers
└── validators/          # Request validation schemas

config/                  # Application configuration
database/
├── migrations/          # Database schema migrations
└── seeders/            # Database seeders

docs/                   # Comprehensive project documentation
├── analysis/           # Technical analysis documents
├── database_design/    # Database schema and design docs
├── milestone_guides/   # Development milestone guides
└── test_guide/         # Testing documentation

start/                  # Application bootstrap
tests/                  # Test suites (unit, functional, integration)
```

### Feature-Based Architecture

The project uses a **domain-driven design** approach with features organized by business domain rather than technical layers. Each feature contains:

- Controllers (HTTP layer)
- Models (data layer)
- Services (business logic)
- Validators (input validation)
- Types (TypeScript definitions)
- Tests (unit and functional)

## Development Guidelines

### Core Principles

#### TypeScript Best Practices

- Use explicit types for variables, parameters, and return values
- Avoid `any` type; prefer specific types or `unknown`
- Follow naming conventions: PascalCase for classes, camelCase for variables/functions
- Use interfaces for object shapes and enums for fixed values
- Write short, focused functions with early returns

#### AdonisJS Architecture

- Follow standard AdonisJS directory structure with domain-driven organization
- Keep controllers lightweight, focused on HTTP concerns only
- Implement business logic in service classes
- Use dependency injection for testability
- Define models with proper TypeScript decorators and typings
- **Always create dedicated controllers** for endpoint logic rather than embedding logic in `routes.ts`

#### Database and ORM Guidelines

- Use PostgreSQL with AdonisJS Lucid ORM
- Implement soft deletes with `deleted_at` column for all user data
- Use UUID primary keys for better scalability and security
- Follow proper indexing strategies for performance
- Use transactions for atomic database operations
- Implement eager loading to prevent N+1 query problems

### Code Organization Patterns

#### Controllers

```typescript
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import TaskService from '#services/task_service'

@inject()
export default class TasksController {
  constructor(private taskService: TaskService) {}

  async index({ response }: HttpContext) {
    const tasks = await this.taskService.getAllTasks()
    return tasks
  }
}
```

#### Services

```typescript
import { inject } from '@adonisjs/core'
import Task from '#models/task'

@inject()
export default class TaskService {
  async createTask(data: TaskCreationData): Promise<Task> {
    return Task.create(data)
  }
}
```

#### Models

```typescript
import { BaseModel, column, beforeSave } from '@adonisjs/lucid/orm'
import { DateTime } from 'luxon'

export default class Task extends BaseModel {
  @column({ isPrimary: true })
  declare id: string

  @column()
  declare title: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null // Soft delete support
}
```

## Development Workflow

### Essential Documentation Workflow

- **Before Starting**: Always consult `docs/deliverables.md` for milestone status and `docs/strategy_plan.md` for project approach
- **For Milestone Work**: Read the corresponding guide in `docs/milestone_guides/` before implementation
- **During Development**: Refer to `docs/findings.md` for contextual insights
- **After Completion**: Update `docs/deliverables.md` by checking off completed items

### Task Master Integration

This project uses Task Master for task-driven development:

- Use MCP Server integration for task management
- Run `next_task` to get the next task to work on
- Use `expand_task` for complex tasks requiring breakdown
- Log progress and update task status regularly

### Testing Strategy

- Create tests for all new features (unit, functional, integration)
- Use Japa testing framework in `tests/` directory
- Follow Test-Driven Development (TDD) when possible
- Ensure all tests pass before considering features complete
- **Test actual functions** - avoid mocking target functions just to pass tests
- Run tests without watch flag during debugging until all pass

## Key Configuration

### Environment Setup

```bash
# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Configure database, Redis, and API keys

# Run migrations
node ace migration:run

# Start development server
npm run dev
```

### Required Environment Variables

- `DB_HOST`, `DB_PORT`, `DB_USER`, `DB_PASSWORD`, `DB_DATABASE` - PostgreSQL configuration
- `REDIS_HOST`, `REDIS_PORT`, `REDIS_PASSWORD` - Redis configuration
- `OPENAI_API_KEY` - OpenAI API for AI features
- `APP_KEY` - Application encryption key
- `JWT_SECRET` - JWT token signing secret

### Database Configuration

- **Primary Database**: PostgreSQL with connection pooling
- **Caching**: Redis for session storage and performance optimization
- **Migrations**: Located in `database/migrations/` with natural sorting
- **Soft Deletes**: Implemented via `adonis-lucid-soft-deletes` package

## Important Files and Directories

### Core Application Files

- `start/routes.ts` - API route definitions (keep lightweight)
- `start/kernel.ts` - Middleware registration and application bootstrap
- `adonisrc.ts` - AdonisJS configuration and providers
- `app/models/base_model.ts` - Base model with custom serialization
- `app/controllers/base_controller.ts` - Shared controller utilities

### Configuration Files

- `config/database.ts` - Database connection configuration
- `config/auth.ts` - Authentication configuration
- `config/cors.ts` - CORS policy configuration
- `config/limiter.ts` - Rate limiting configuration

### Documentation

- `docs/authentication.md` - Comprehensive authentication guide
- `docs/database_design/` - Database schema and design documentation
- `docs/database_design/privacy_policy_performance_strategy.md` - Privacy system performance strategy
- `docs/test_guide/` - Testing guides and examples

### Development Tools

- `.cursor/rules/` - Cursor AI development rules and guidelines
- `eslint.config.js` - ESLint configuration for code quality
- `tsconfig.json` - TypeScript compiler configuration

## API Structure

### Authentication Endpoints

- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login (returns access + refresh tokens)
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - Logout and invalidate tokens

### Core Resource Endpoints

- `/api/v1/profile` - User profile management
- `/api/v1/tasks` - Task management with AI assistance
- `/api/v1/events` - Event scheduling
- `/api/v1/contacts` - Contact and relationship management
- `/api/v1/privacy-profiles` - Privacy policy templates

### Authentication

- **Token Type**: JWT with 15-minute access tokens and 7-day refresh tokens
- **Header Format**: `Authorization: Bearer <access_token>`
- **Middleware**: Applied to all protected routes via route groups

## Performance Considerations

### Database Optimization

- Strategic indexing on frequently queried columns
- Composite indexes for complex query patterns
- Partial indexes for common filtered queries
- JSONB optimization with GIN indexes

### Caching Strategy

- Redis caching for relationship context (15-minute TTL)
- Query optimization with eager loading
- Connection pooling for database efficiency

### Privacy Policy System

- Template-based rule evaluation with priority ordering
- In-memory policy processing after data loading
- Optimized query flow: 2 queries on cache hit, 3 on cache miss

## Security Best Practices

### Data Protection

- Soft deletes for audit compliance and data retention
- UUID primary keys for security and scalability
- Input validation using VineJS validators
- SQL injection prevention through parameterized queries

### Authentication Security

- JWT tokens with short expiration times
- Refresh token rotation
- Rate limiting on authentication endpoints
- Secure password hashing

### Privacy Controls

- Sophisticated template-based privacy system
- Granular visibility controls (hidden, busy_only, title_only, full_details)
- Viewer scope types (public, contacts, specific groups)
- Activity-level privacy overrides

## Common Development Tasks

### Adding New Features

1. Create feature directory in `app/features/`
2. Implement model with proper TypeScript types
3. Create service class for business logic
4. Build controller using base controller utilities
5. Add validators for request validation
6. Define routes in `start/routes.ts`
7. Write comprehensive tests
8. Update documentation

### Database Changes

1. Create migration: `node ace make:migration <name>`
2. Define schema changes with proper indexes
3. Run migration: `node ace migration:run`
4. Update model definitions
5. Add/update tests for new schema

### Testing New Code

1. Write unit tests for services and models
2. Create functional tests for API endpoints
3. Run tests: `npm test`
4. Ensure coverage for edge cases
5. Test authentication and authorization

## Troubleshooting

### Common Issues

- **Database Connection**: Check PostgreSQL service and credentials
- **Redis Connection**: Verify Redis server is running
- **Migration Errors**: Ensure database exists and user has proper permissions
- **Test Failures**: Check test database configuration and data setup
- **Authentication Issues**: Verify JWT secret and token expiration settings

### Development Tools

- **Hot Reloading**: `npm run dev` for automatic server restart
- **Database Reset**: `node ace migration:rollback --batch=0` then `node ace migration:run`
- **Code Quality**: `npm run lint` and `npm run format`
- **Type Checking**: `npm run typecheck`

## Additional Resources

### Documentation

- [AdonisJS Documentation](https://docs.adonisjs.com/)
- [Lucid ORM Guide](https://docs.adonisjs.com/guides/database/introduction)
- [VineJS Validation](https://vinejs.dev/)

### Project-Specific Guides

- `docs/authentication.md` - Authentication system details
- `docs/database_design/README.md` - Database architecture overview
- `docs/test_guide/` - Testing strategies and examples
- `.cursor/rules/` - Development guidelines and best practices

---

_This guide provides the foundation for effective development on the SkedAI backend. Always refer to the specific documentation files for detailed implementation guidance._
