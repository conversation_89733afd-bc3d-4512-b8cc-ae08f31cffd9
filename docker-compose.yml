services:
  db:
    restart: unless-stopped
    image: postgres
    environment:
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_DB=${DB_DATABASE}
    container_name: skedai-db
    env_file:
      - .env
    volumes:
      - postgres_volume:/var/lib/postgresql/data
    ports:
      - '5432:5432'
    expose:
      - '5432'
    configs:
      - source: init.sql
        target: /docker-entrypoint-initdb.d/init.sql

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: skedai-backend
    environment:
      - PORT=${PORT}
      - PG_HOST=${DB_HOST}
    env_file:
      - .env
    ports:
      - '3333:3333'
      - 9229:9229
    expose:
      - '3333'
    depends_on:
      - db
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run dev

  pgadmin:
    image: dpage/pgadmin4
    container_name: skedai-pgadmin4
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=pgadmin4
      - PGADMIN_CONFIG_SERVER_MODE=False
      - PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED=False
    ports:
      - '5050:80'
    depends_on:
      - db
    env_file:
      - .env
    entrypoint: /bin/sh -c "chmod 600 /pgpass; /entrypoint.sh;"
    user: root
    configs:
      - source: servers.json
        target: /pgadmin4/servers.json
      - source: pgpass
        target: /pgpass

volumes:
  postgres_volume:

configs:
  pgpass:
    content: ${DB_HOST}:${DB_PORT}:*:${DB_USER}:${DB_PASSWORD}
  servers.json:
    content: |
      {"Servers": {"1": {
        "Group": "Servers",
        "Name": "${DB_DATABASE}",
        "Host": "${DB_HOST}",
        "Port": ${DB_PORT},
        "MaintenanceDB": "${DB_DATABASE}",
        "Username": "${DB_USER}",
        "PassFile": "/pgpass",
        "SSLMode": "prefer"
      }}}
  init.sql:
    content: |
      CREATE USER ${DB_USER} with encrypted password '${DB_PASSWORD}';
      CREATE DATABASE ${DB_DATABASE};
      GRANT ALL PRIVILEGES ON ${DB_DATABASE} TO ${DB_USER};
