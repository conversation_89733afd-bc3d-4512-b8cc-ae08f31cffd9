# Milestone 2.2: Create User Model and Database Migration

**Objective**: Establish the foundational User model and corresponding database migration for authentication and user data management.

**Process**:

1. **Database Schema Design**:

   - Define the `User` model schema with fields:
     - `user_id`: Primary Key, Unique Identifier (UUID preferred).
     - `username`: String, Unique username for each user.
     - `email`: String, User's email address.
     - `password_hash`: String, Securely hashed password.
     - `created_at`: Timestamp, When the user was created.
     - `updated_at`: Timestamp, When the user was last updated.
     - `deleted_at`: Timestamp, For soft delete functionality.
     - `profile_id`: Reference to the user's profile (restricted to one profile per user currently).
   - Define the `Profile` model schema with fields:
     - `profile_id`: Primary Key, connected to the user account.
     - `first_name`: String, User's first name.
     - `last_name`: String, User's last name.
     - `birth_date`: Date, User's date of birth.

2. **Model Creation**:

   - Use AdonisJS Lucid ORM to create the `User` model in `app/Models/User.ts`.
   - Implement necessary decorators for fields and relationships.
   - Create the `Profile` model in `app/Models/Profile.ts` with a one-to-one relationship to `User`.

3. **Migration File Generation**:

   - Generate migration files for `users` and `profiles` tables using AdonisJS CLI (`node ace make:migration`).
   - Define table structure in migration files to match the schema outlined above.
   - Include foreign key constraints for `profile_id` in the `users` table.

4. **Database Configuration**:

   - Ensure the database connection is properly configured in `config/database.ts`.
   - Run migrations using `node ace migration:run` to apply the schema to the database.

5. **Relationships and Type Safety**:

   - Define relationships in models using Lucid ORM decorators (e.g., `@hasOne` for User to Profile).
   - Ensure TypeScript types are explicitly defined for all fields and relationships.

6. **Initial Testing**:

   - Write basic unit tests for model creation and relationship integrity in `tests/unit/models`.
   - Use Japa testing framework to verify that models can be instantiated and saved to the database.

7. **Documentation**:
   - Update `docs/findings.md` with any observations or code snippets related to model and migration setup.
   - Note any deviations from the planned schema or issues encountered during migration.

**Best Practices**:

- Follow TypeScript best practices by using explicit types for all variables and return values.
- Adhere to AdonisJS architecture by keeping models focused on data structure and relationships.
- Implement soft delete functionality for user data protection.
- Ensure migrations are reversible to support database rollbacks.

**Completion Criteria**:

- [ ] User model defined with all specified fields and relationships.
- [ ] Profile model defined and linked to User model.
- [ ] Migration files created and successfully applied to the database.
- [ ] Basic unit tests for models pass successfully.
- [ ] Documentation updated with relevant findings.
