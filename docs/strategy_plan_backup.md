# Strategy Plan for Skedai Backend Development

## Project Overview

Skedai is a backend server developed as part of a comprehensive application focused on task management and event/book appointment scheduling. It combines the functionalities of a task management app with features similar to Calendly. The task management component includes basic todo lists, sub-tasks, task invitations (via link or account invite), a pomodoro timer, and detailed task data such as title, description, locations mentioned, people mentioned, tags, notes, due date, remind time, and other metadata.

## Project Goal

The primary goal of Skedai is to empower users to organize their day or week using the timeboxing method. This approach allows users to allocate specific durations to tasks, facilitating efficient daily or weekly planning and organization.

## Technical Architecture

### Framework and Language

- **AdonisJS**: The backend is built using AdonisJS, a Node.js framework that provides a robust structure for API development.
- **TypeScript**: All code is written in TypeScript to ensure type safety, better maintainability, and scalability.

### Architectural Principles

- **Clean Architecture**: Emphasis on separation of concerns, with distinct models for authentication (`User`) and user data (`Profile`).
- **RESTful API Design**: Endpoints are designed to be intuitive and follow RESTful conventions.
- **Security**: Implementation of secure password hashing, authentication tokens, and data validation.

## Current Implementation Plan: User API

The immediate focus is on developing a comprehensive User API to handle authentication and basic user data management. This includes:

### Database Schema

- **User Model**:
  - `user_id`: Primary Key, Unique Identifier (UUID preferred).
  - `username`: String, Unique username for each user.
  - `email`: String, User's email address.
  - `password_hash`: String, Securely hashed password.
  - `created_at`: Timestamp, When the user was created.
  - `updated_at`: Timestamp, When the user was last updated.
  - `deleted_at`: Timestamp, For soft delete functionality.
  - `profile_id`: Reference to the user's profile (restricted to one profile per user currently).
- **Profile Model**:
  - `profile_id`: Primary Key, connected to the user account.
  - `first_name`: String, User's first name.
  - `last_name`: String, User's last name.
  - `birth_date`: Date, User's date of birth.

### Development Steps

1. **Database Setup**: Configure the database with AdonisJS Lucid ORM, defining models and relationships.
2. **ORM Queries**: Implement queries for CRUD operations on `User` and `Profile` models.
3. **API Endpoints**: Create RESTful endpoints for user registration, login, profile management, etc.
4. **Database Seeding**: Add initial data for testing and development purposes.
5. **Testing**: Write unit, functional, and integration tests using Japa to ensure reliability and correctness.

## Future Features and Roadmap

Beyond the User API, the following features are planned for future implementation:

- **Task Management**: Support for todo lists, sub-tasks, and detailed task metadata.
- **Collaboration**: Enable task invitations via links or account invites.
- **Scheduling**: Implement event and appointment booking akin to Calendly.
- **Pomodoro Timer**: Integrate a timer feature for productivity.
- **Profile Expansion**: Move user data to profiles, keeping the user account strictly for authentication purposes.

## Iterative Development Strategy

To ensure steady progress and maintainable code, the development will follow an iterative approach:

1. **Phase 1: User API Completion**
   - Finalize user authentication and profile management.
   - Ensure all tests pass and endpoints are secure.
2. **Phase 2: Task Management Core**
   - Develop models, services, and endpoints for tasks and sub-tasks.
   - Implement basic task metadata handling.
3. **Phase 3: Collaboration and Scheduling**
   - Add features for task invitations and event scheduling.
   - Enhance API for multi-user interactions.
4. **Phase 4: Productivity Tools and Refinement**
   - Integrate pomodoro timer functionality.
   - Refine user experience based on feedback and testing.
5. **Continuous Testing and Updates**
   - Follow Test-Driven Development (TDD) principles for all new features.
   - Update this strategy plan and project documentation (`deliverables.md`, `findings.md`) with progress and insights.

## Conclusion

This strategy plan outlines the development path for the Skedai backend, ensuring a structured approach to building a robust task management and scheduling application. By adhering to AdonisJS and TypeScript best practices, focusing on clean architecture, and iterating through development phases, we aim to deliver a scalable and user-friendly backend solution.
