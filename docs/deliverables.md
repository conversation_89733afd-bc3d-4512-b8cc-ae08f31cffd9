# Skedai Backend Deliverables

## Legend

- [ ] Not started
- [ ] In progress
- [x] Completed

This document outlines the deliverables for each milestone, organized in a linear and iterative sequence.

## Milestone 1: Migrate Vercel AI SDK to LangchainJS

1. [x] Identify all existing Vercel AI SDK imports and usage patterns across the codebase.
2. [x] Define a common AIService interface to abstract AI provider calls.
3. [x] Implement a LangchainJS adapter class that satisfies the AIService interface.

## Milestone 2: Foundation & Authentication

1. [x] Setup AdonisJS project skeleton
2. [ ] Create User model and database migration ([Guide](milestone_guides/milestone_2_2_user_model_migration.md))
3. [ ] Implement registration and login endpoints (email/password)
4. [ ] Configure authentication middleware
5. [ ] Write unit and integration tests for authentication flows

## Milestone 3: Core Task Management

1. [ ] Define Task model (title, description, due_date, completed_status)
2. [ ] Implement CRUD API endpoints for tasks
3. [ ] Implement task listing and retrieval for authenticated users
4. [ ] Write tests covering success, validation errors, and edge cases

## Milestone 4: Task Metadata Expansion

1. [ ] Extend Task model with sub-tasks (self-referencing), tags, notes, remind_time
2. [ ] Implement relationships and validation logic
3. [ ] Create and update endpoints for metadata features
4. [ ] Write tests for metadata integrity and edge cases

## Milestone 5: Timeboxing & Scheduling

1. [ ] Add `duration` and `scheduled_start_time` fields to Task model
2. [ ] Develop endpoints for calendar/timeline views of tasks
3. [ ] (Optional) Plan integration with external calendar APIs
4. [ ] Write tests for scheduling logic and timebox accuracy

## Milestone 6: AI Integration - Natural Language Task Creation

1. [ ] Finalize LangchainJS and OpenAI integration setup
2. [ ] Design prompts and chains to parse natural language into structured tasks
3. [ ] Implement API endpoint for natural language task creation
4. [ ] Optimize prompts for minimal token usage
5. [ ] Write tests for parsing accuracy and error handling

## Milestone 7: AI Integration - Information Retrieval

1. [ ] Develop LangchainJS-based services for answering task queries (e.g., "tasks for today")
2. [ ] Implement retrieval mechanisms (embeddings or structured querying)
3. [ ] Create API endpoint for natural language queries
4. [ ] Write tests for retrieval correctness and edge cases

## Milestone 8: Collaboration Features

1. [ ] Design task sharing via secure links or account invites
2. [ ] Implement permissions and access control models
3. [ ] Extend Task model with `assigned_users`, `mentioned_people`, `locations`
4. [ ] Write tests for sharing flows and permission enforcement

## Milestone 9: Pomodoro Timer

1. [ ] Implement Pomodoro session model linked to tasks
2. [ ] Create endpoints for starting, stopping, and querying timer state
3. [ ] Write tests for timer functionality and edge cases

## Milestone 10: Testing Strategy Implementation

1. [ ] Establish unit testing framework for services
2. [ ] Establish integration testing pipeline for API endpoints
3. [ ] Define AI testing strategy (mock LLM responses, edge cases)
4. [ ] Integrate tests into CI/CD workflow

## Milestone 11: Documentation & Future Enhancements

1. [ ] Draft comprehensive API documentation (OpenAPI/Swagger)
2. [ ] Setup CI/CD pipeline (GitHub Actions, GitLab CI)
3. [ ] Implement monitoring and logging solutions
4. [ ] Configure environment management (dev, test, staging, prod)
5. [ ] Conduct security review and dependency scanning
6. [ ] Plan scalability enhancements and schedule regular strategy reviews
