# Contact Management API Testing Guide

This guide provides comprehensive testing instructions for the Contact Management System API.

## Base URL

```
http://localhost:3333/api/v1/contacts
```

## Authentication Required

All endpoints require authentication. You'll need to:

1. Register/Login via `/api/v1/auth/login` or `/api/v1/auth/register`
2. Include the Bearer token in the Authorization header: `Authorization: Bearer <your_token>`

## API Endpoints Overview

### 1. Contact Request Management

#### Send Contact Request

```bash
POST /api/v1/contacts/requests
Content-Type: application/json
Authorization: Bearer <token>

{
  "addresseeUserId": "uuid-of-user-to-contact"
}
```

**Response (201):**

```json
{
  "success": true,
  "message": "Contact request sent successfully",
  "data": {
    "id": "contact-uuid",
    "requesterUserId": "sender-uuid",
    "addresseeUserId": "receiver-uuid",
    "status": "pending",
    "createdAt": "2025-01-01T00:00:00.000Z",
    "requester": {
      /* user data */
    },
    "addressee": {
      /* user data */
    }
  }
}
```

#### Respond to Contact Request

```bash
PATCH /api/v1/contacts/requests/{contactId}/respond
Content-Type: application/json
Authorization: Bearer <token>

{
  "status": "accepted" // or "declined" or "blockedByAddressee"
}
```

#### Block Contact

```bash
PATCH /api/v1/contacts/{contactId}/block
Authorization: Bearer <token>
```

#### Get All Contacts

```bash
GET /api/v1/contacts
Authorization: Bearer <token>

# Query parameters (all optional):
# - status: pending|accepted|declined|blockedByRequester|blockedByAddressee
# - includeRequests: true|false (default: true)
# - includeReceived: true|false (default: true)

# Examples:
GET /api/v1/contacts?status=accepted
GET /api/v1/contacts?includeRequests=true&includeReceived=false
```

### 2. Contact List Management

#### Create Contact List

```bash
POST /api/v1/contacts/lists
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Work Contacts"
}
```

#### Get All Contact Lists

```bash
GET /api/v1/contacts/lists
Authorization: Bearer <token>
```

#### Get Specific Contact List

```bash
GET /api/v1/contacts/lists/{listId}
Authorization: Bearer <token>
```

#### Update Contact List

```bash
PATCH /api/v1/contacts/lists/{listId}
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Updated List Name"
}
```

#### Delete Contact List

```bash
DELETE /api/v1/contacts/lists/{listId}
Authorization: Bearer <token>
```

### 3. Contact List Member Management

#### Add Contact to List

```bash
POST /api/v1/contacts/lists/{listId}/members
Content-Type: application/json
Authorization: Bearer <token>

{
  "userId": "uuid-of-accepted-contact"
}
```

#### Remove Contact from List

```bash
DELETE /api/v1/contacts/lists/{listId}/members/{memberId}
Authorization: Bearer <token>
```

#### Get List Members

```bash
GET /api/v1/contacts/lists/{listId}/members
Authorization: Bearer <token>
```

## Testing Workflow

### 1. Prerequisites

1. Start the server: `npm run dev`
2. Create 2 test users via `/api/v1/auth/register`
3. Get authentication tokens for both users

### 2. Contact Request Flow Test

```bash
# User A sends request to User B
curl -X POST http://localhost:3333/api/v1/contacts/requests \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <user_a_token>" \
  -d '{"addresseeUserId": "<user_b_id>"}'

# User B accepts the request
curl -X PATCH http://localhost:3333/api/v1/contacts/requests/<contact_id>/respond \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <user_b_token>" \
  -d '{"status": "accepted"}'

# Both users can now see the accepted contact
curl -X GET http://localhost:3333/api/v1/contacts?status=accepted \
  -H "Authorization: Bearer <user_a_token>"
```

### 3. Contact List Flow Test

```bash
# User A creates a contact list
curl -X POST http://localhost:3333/api/v1/contacts/lists \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <user_a_token>" \
  -d '{"name": "My Friends"}'

# User A adds User B to the list
curl -X POST http://localhost:3333/api/v1/contacts/lists/<list_id>/members \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <user_a_token>" \
  -d '{"userId": "<user_b_id>"}'

# User A views the list members
curl -X GET http://localhost:3333/api/v1/contacts/lists/<list_id>/members \
  -H "Authorization: Bearer <user_a_token>"
```

## Expected Error Scenarios

### Authentication Errors (403)

- Missing Authorization header
- Invalid or expired token

### Bad Request Errors (400)

- Self-contact attempt
- Duplicate contact request
- Invalid status values
- Missing required parameters

### Not Found Errors (404)

- Contact/List/Member doesn't exist
- User doesn't exist

### Authorization Errors (403)

- Trying to respond to someone else's contact request
- Accessing another user's contact lists
- Managing another user's list members

## Success Response Format

All successful responses follow this format:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    /* response data */
  }
}
```

## Error Response Format

All error responses follow this format:

```json
{
  "status": 400,
  "code": "E_BAD_REQUEST",
  "message": "Descriptive error message"
}
```

## Business Rules Enforced

1. **No Self-Contact**: Users cannot send contact requests to themselves
2. **One Relationship Per Pair**: Prevents duplicate contact requests between users
3. **Response Authorization**: Only addressees can respond to contact requests
4. **Accepted Contacts Only**: Only accepted contacts can be added to lists
5. **Ownership Required**: Users can only manage their own lists and members
6. **Unique List Names**: Contact list names must be unique per user
7. **Relationship Validation**: Contact list membership requires an accepted contact relationship

## Database Tables Created

- `user_contact`: Stores contact relationships and their status
- `user_contact_list`: Stores user-created contact lists
- `contact_list_member`: Junction table for list memberships

The Contact Management API is now fully functional and ready for testing!
