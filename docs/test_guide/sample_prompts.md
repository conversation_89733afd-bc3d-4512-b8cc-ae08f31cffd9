Here are 15 diverse and natural-sounding task prompts that test various edge cases and features of your task management system:

### Time-sensitive work task

"Prepare the quarterly sales report by Friday 3pm, need to present it to the team at 4pm. Remind me 1 hour before the presentation."

### Recurring personal task

"Water the plants every Monday and Thursday at 7pm, starting tomorrow."

### Location-based with multiple options

"Pick up dry cleaning after work today - they close at 7pm. Location: either 123 Main St or 456 Oak Ave, whichever is on my way home."

### Collaborative task

"Schedule a team lunch with <PERSON>, <PERSON>, and <PERSON> next week. Find a date that works for everyone and book a table at a nice Italian place."

### Task with dependencies

"After finishing the project proposal (Task #42), send it to the client for review. Set a reminder to follow up in 3 business days if no response."

### Shopping list with categories

"Grocery shopping at Walmart tomorrow: milk, eggs, bread (dairy/essentials), chicken, rice, and vegetables (dinner). Don't forget to use the 10% off coupon!"

### Health-related with priority

"Take blood pressure medication daily at 8am. High priority - can't miss this!"

### Event planning

"Mom's 60th birthday is on June 15th. Order cake, buy gifts, and book dinner at her favorite restaurant. Invite family members: Dad, <PERSON>, and <PERSON>."

### Learning/development

"Complete React Native course module 3 by end of week. Schedule 2 hours on Tuesday and Thursday evenings for focused study time."

### Financial task

"Pay electricity bill due on the 5th. Set reminder for the 1st of each month. Account #12345, website: myelectric.com."

### Vague but time-sensitive

"Call John about the project when he's back from vacation next Monday. His number is 555-1234."

### Task with metadata

"Organize digital photos from summer vacation. Tags: vacation2023, family, beach. Need to sort by date and create an album."

### Multiple locations

"Return library books to either downtown branch or university library by Friday. Books are due on Saturday."

### Task with conditional reminder

"If it's going to rain tomorrow, reschedule the park picnic. Check weather in the morning and notify the group chat."

### Complex project task

"Redesign website homepage - discuss with design team on Monday, get mockups by Wednesday, review with stakeholders by Friday. High priority. Tags: design, website, urgent"

These prompts test various aspects of your system:

- Different date/time formats
- Multiple locations
- People tagging
- Priority levels
- Reminders
- Task dependencies
  Categories/tags
  Recurring tasks
  Conditional logic
  Different domains (work, personal, health, etc.)
  Various levels of detail and structure
