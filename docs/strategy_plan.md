# Skedai Backend Development Strategy

**Deliverables:** See [deliverables.md](deliverables.md) for linear, iterative deliverables list.
**Findings:** Record code snippets, observations, and contextual knowledge in [findings.md](findings.md) under the relevant milestone headings to maintain context even if chat history is cleared.

## 1. Introduction

This document outlines the strategic plan for the iterative development of the Skedai backend server. Skedai aims to be a comprehensive task management and event scheduling application, merging features of traditional todo list apps with appointment scheduling capabilities similar to Calendly. The core goal is to empower users to organize their time effectively using the timeboxing method, facilitated by a minimal yet feature-rich interface with powerful AI capabilities for natural language interaction.

## 2. Guiding Principles

The development process will adhere to the following principles:

- **User-Centric Design:** Prioritize exceptional User Experience (UX) and User Interface (UI) design, focusing on ease of use, intuitive workflows, and thoughtful feature implementation.
- **Cost Optimization:** Minimize operational costs, particularly OpenAI API token usage, through efficient design, prompt engineering, and potential caching strategies.
- **Maintainable Architecture:** Employ a feature-slicing architecture to promote modularity, scalability, and ease of maintenance.
- **Quality & Reliability:** Ensure code quality and feature reliability through comprehensive testing (unit, integration) for every implemented feature slice.
- **Iterative Development:** Build the application incrementally, focusing on delivering functional feature slices in each iteration.

## 3. Technology Stack

- **Backend Framework:** AdonisJS (v6) with TypeScript
- **Database:** PostgreSQL (Recommended, flexible based on deployment)
- **ORM:** AdonisJS Lucid
- **LLM Integration:** LangchainJS
- **AI Provider:** OpenAI API
- **Testing:** AdonisJS inbuilt testing suite (Japa)

## 4. Architecture: Feature Slicing

We will adopt a feature-slicing approach. Each major feature area will be developed as a vertical slice through the application stack (routes, controllers, services, models, tests). This promotes cohesion within a feature and reduces coupling between different features.

**Example Slice Structure (e.g., `tasks`):**

```
app/
├── features/
│   └── tasks/
│       ├── controllers/
│       │   └── tasks_controller.ts
│       ├── services/
│       │   └── task_service.ts
│       ├── validators/
│       │   └── create_task_validator.ts
│       ├── models/
│       │   └── task.ts
│       ├── exceptions/
│       │   └── task_not_found_exception.ts
│       └── routes.ts
├── tests/
│   └── functional/
│       └── features/
│           └── tasks/
│               └── create_task.spec.ts
start/
├── routes.ts # Imports feature routes
...
```

## 5. Core Modules (Initial Feature Slices)

Development will likely proceed through these core modules, though the order may be adjusted:

1.  **Foundation & Authentication:**
    - Setup AdonisJS project structure.
    - Implement User model and database migration.
    - Implement user registration and login (e.g., email/password, potentially OAuth later).
    - Setup authentication middleware.
2.  **Core Task Management:**
    - Task model (including title, description, due_date, completed_status).
    - CRUD API endpoints for tasks (Create, Read, Update, Delete).
    - Basic task listing and retrieval for the authenticated user.
3.  **Task Metadata Expansion:**
    - Extend Task model and APIs to include: sub-tasks (potentially self-referencing), tags, notes, remind_time.
    - Implement logic for handling relationships (e.g., parent-child tasks).
4.  **Timeboxing & Scheduling:**
    - Add `duration` and `scheduled_start_time` fields to Tasks.
    - Develop API endpoints/logic to view tasks on a calendar/timeline (supporting timeboxing visualization).
    - Consider integration with external calendar APIs (future).
5.  **AI Integration - Natural Language Task Creation:**
    - Setup LangchainJS and OpenAI integration.
    - Develop a service to parse natural language input (e.g., \"Create a task 'Buy milk' for tomorrow afternoon\") into structured task data.
    - Integrate this service with a dedicated API endpoint.
    - Focus on robust prompt engineering to extract key entities (title, date, time) accurately and efficiently.
6.  **AI Integration - Information Retrieval:**
    - Develop services using LangchainJS/OpenAI to answer user queries about their tasks (e.g., \"What are my tasks for today?\", \"Show me tasks tagged 'urgent'\").
    - Implement retrieval mechanisms (potentially using embeddings or structured data querying guided by the LLM).
7.  **Collaboration Features:**
    - Implement task sharing/invitations (via secure link or user accounts).
    - Define permissions and access control for shared tasks.
    - Add `assigned_users`, `mentioned_people`, `locations` fields/relationships.
8.  **Pomodoro Timer:**
    - Implement backend logic to support starting, stopping, and tracking Pomodoro sessions associated with tasks.
    - API endpoints for timer state management.

## 6. Iterative Development Workflow

Each feature slice or iteration will follow these steps:

1.  **Define Scope:** Clearly define the requirements and boundaries of the feature/enhancement for the current iteration.
2.  **API Design:** Design the necessary API endpoints, request/response structures, and validation rules using AdonisJS conventions.
3.  **Schema Design:** Define or update database models (`app/models`), migrations (`database/migrations`), and relationships. Run migrations.
4.  **Implementation:**
    - Write route definitions (`start/routes.ts` or feature-specific `routes.ts`).
    - Implement controllers (`app/features/.../controllers`) to handle HTTP requests/responses.
    - Implement validators (`app/features/.../validators`) for request data validation.
    - Implement services (`app/features/.../services`) to encapsulate business logic.
    - Interact with the database via Lucid models.
5.  **AI Integration (If Applicable):**
    - Design prompts for specific LLM tasks.
    - Implement LangchainJS chains/agents within services.
    - Prioritize efficient token usage (e.g., specific instructions, few-shot examples if needed, concise context).
    - Handle potential errors from the AI provider.
6.  **Testing:**
    - Write unit tests for services and complex logic.
    - Write integration tests for API endpoints, covering success cases, validation errors, and edge cases.
    - Run tests frequently (`node ace test`).
7.  **Refinement:** Analyze test results and refine code for correctness, clarity, and adherence to principles. Address any bugs identified.
8.  **Documentation (Optional but Recommended):** Add comments or generate API documentation (e.g., using OpenAPI).

## 7. AI Strategy Specifics

- **Focus:** Start with high-impact, clearly defined AI features: natural language task creation and basic information retrieval.
- **Efficiency:**

  - **Precise Prompts:** Craft prompts that clearly instruct the LLM on the desired output format (e.g., JSON) and the specific information to extract/generate.
  - **Context Minimization:** Provide only the necessary context to the LLM for the specific task. Avoid sending large amounts of unnecessary user data.
  - **Model Selection:** Use appropriate OpenAI models (e.g., potentially faster/cheaper models for simpler tasks if performance is adequate).
  - **Caching:** Explore caching LLM responses for identical or very similar queries, especially for information retrieval (use with caution regarding data freshness).
  - **Structured Data First:** Where possible, use traditional database queries first and only leverage the LLM when natural language understanding or generation is essential.

- **Migration from Vercel AI SDK to LangchainJS:**
  1. Audit existing Vercel AI SDK usage in the codebase.
  2. Catalog AI workflows and map to LangchainJS components (chains, agents, memory).
  3. Implement LangchainJS chains to replicate current AI features.
  4. Replace Vercel AI SDK calls with LangchainJS integrations in the service layer.
  5. Update tests to validate LangchainJS behavior and flow.
  6. Remove Vercel AI SDK dependency and clean up deprecated code.

## 8. Testing Strategy Specifics

- **Unit Tests:** Focus on testing individual functions and methods within services, ensuring business logic works correctly in isolation. Mock dependencies like database calls or external APIs (including OpenAI).
- **Integration Tests:** Test the flow through API endpoints, including routing, validation, controller logic, service execution, and database interaction. Use a dedicated test database.
- **AI Testing:** Testing AI interactions can be challenging. Focus on:
  - Testing the data preparation _before_ sending it to the LLM.
  - Testing the parsing and handling of the LLM's response.
  - Having a few representative test cases with fixed inputs/outputs (understanding that LLM output can vary).
  - Testing error handling for API failures or unexpected LLM responses.

## 9. Suggestions for Improvement & Future Considerations

- **Minimum Viable Product (MVP) Definition:** Clearly define the smallest set of features (likely including Core Task Management and basic Timeboxing) that delivers initial value and allows for user feedback.
- **API Specification:** Maintain an OpenAPI (Swagger) specification for the backend API. This aids frontend development (Flutter, React) and ensures clarity. AdonisJS has tools to help generate this.
- **CI/CD Pipeline:** Set up a Continuous Integration/Continuous Deployment pipeline early (e.g., using GitHub Actions, GitLab CI) to automate testing and deployment.
- **Monitoring & Logging:** Implement structured logging throughout the application. Set up monitoring tools to track application performance, errors, and resource usage (including API costs).
- **Environment Management:** Configure separate environments (development, testing, staging, production) with appropriate settings (database credentials, API keys).
- **Security:** Regularly review security best practices (input validation, authentication, authorization, dependency scanning).
- **Scalability:** While focusing on clarity first, keep potential future scalability needs in mind (e.g., efficient database queries, potential for background job processing for intensive tasks).
- **Regular Reviews:** Conduct regular code reviews and strategy reviews to ensure alignment and address any emerging challenges.

This document serves as a living guide and should be updated as the project evolves and requirements become clearer.
