# Skedai Backend Findings

This file captures code snippets, observations, and contextual knowledge organized by milestone. When exploring the codebase, store any useful code snippet or insight under the heading of the milestone you're working on to maintain context even if the chat history is cleared.

## PROJECT ARCHITECTURE DISCOVERY (2025-01-25)

### **CRITICAL FINDING: Current Architecture is Polymorphic, NOT Flattened**

**Architecture Pattern:** Polymorphic relationships using `activityType` + `activityId`

- Separate `tasks` and `events` tables with all activity fields included directly
- Privacy policy assignments use polymorphic references: `activity_type` ENUM + `activity_id` UUID
- No unified activities table in active use (Activity model exists but non-functional)

**Evidence:**

- `privacy_policy_assignments` migration: polymorphic relationship to tasks/events
- Tasks migration comment: "Core activity fields (flattened from activities table)" - indicates migration FROM unified approach
- Activity model has placeholder methods throwing "not yet implemented" errors

**Implementation Status:** ~85% complete, significantly beyond original scope

- 23 tables implemented vs ~10 originally planned
- Advanced privacy system with template-based rules
- Complete contact and group management systems
- AI integration with LangchainJS (migration from Vercel AI SDK completed)

## Milestone 1: Migrate Vercel AI SDK to LangchainJS ✅ COMPLETED

- Located `import { generateObject } from 'ai'` in `app/services/task_service.ts` for schema-based object generation.
- Located `import { openai } from '@ai-sdk/openai'` in `app/services/task_service.ts` specifying the LLM model.
- Found commented-out `// import { google } from '@ai-sdk/google'` in `app/services/task_service.ts` as a potential alternate provider.
- Noted AI call entry in `app/controllers/tasks_controller.ts` via `this.taskService.aiPrompt({ prompt })`.
- **CONFIRMED:** LangchainJS implementation active in `langchain_ai_service.ts`

## Milestone 2: Foundation & Authentication ✅ COMPLETED

## Milestone 3: Core Task Management

## Milestone 4: Task Metadata Expansion

## Milestone 5: Timeboxing & Scheduling

## Milestone 6: AI Integration - Natural Language Task Creation

## Milestone 7: AI Integration - Information Retrieval

## Milestone 8: Collaboration Features

## Milestone 9: Pomodoro Timer

## Milestone 10: Testing Strategy Implementation

## Milestone 11: Documentation & Future Enhancements
