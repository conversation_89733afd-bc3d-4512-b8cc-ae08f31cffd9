# Authentication & User Management System

This document provides a comprehensive overview of the authentication system implemented in the Skedai AdonisJS backend. It covers the User and Profile models, authentication endpoints, JWT token generation, refresh token mechanism, and middleware implementation.

## Table of Contents

- [Models](#models)
  - [User Model](#user-model)
  - [Profile Model](#profile-model)
  - [RefreshToken Model](#refreshtoken-model)
- [Authentication Validators](#authentication-validators)
- [Endpoints](#endpoints)
  - [Registration](#registration)
  - [Login](#login)
  - [Token Refresh](#token-refresh)
  - [Logout](#logout)
- [Authentication Middleware](#authentication-middleware)
- [Route Configuration](#route-configuration)
- [Using Authentication in API Requests](#using-authentication-in-api-requests)

## Models

### User Model

The User model is located at `app/models/user.ts` and implements both authentication and soft-delete functionality.

#### Fields

| Field Name   | Type     | Description                                    |
| ------------ | -------- | ---------------------------------------------- |
| id           | string   | Primary key, UUID                              |
| username     | string   | User's unique username                         |
| email        | string   | User's unique email address                    |
| passwordHash | string   | Hashed password (using Scrypt), not serialized |
| createdAt    | DateTime | Timestamp when the record was created          |
| updatedAt    | DateTime | Timestamp when the record was last updated     |
| deletedAt    | DateTime | Soft-delete timestamp (null if not deleted)    |

#### Relationships

- `refreshTokens` - One-to-many relationship with RefreshToken model
- `profile` - One-to-one relationship with Profile model

#### Code Example

```typescript
export default class User extends compose(BaseModel, AuthFinder, SoftDeletes) {
  static accessTokens = DbAccessTokensProvider.forModel(User, {
    expiresIn: '15 minutes',
    prefix: 'oat_',
    table: 'auth_access_tokens',
    type: 'auth_token',
    tokenSecretLength: 40,
  })

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare username: string

  @column()
  declare email: string

  @column({ serializeAs: null })
  declare passwordHash: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  @hasMany(() => RefreshToken)
  declare refreshTokens: HasMany<typeof RefreshToken>

  @hasOne(() => Profile)
  declare profile: HasOne<typeof Profile>
}
```

### Profile Model

The Profile model is related to the User model and contains additional user information.

#### Fields

| Field Name     | Type     | Description                                   |
| -------------- | -------- | --------------------------------------------- |
| id             | string   | Primary key, UUID                             |
| userId         | string   | Foreign key to User model (unique constraint) |
| firstName      | string   | User's first name                             |
| lastName       | string   | User's last name                              |
| birthDate      | DateTime | User's birth date (optional)                  |
| profilePicture | string   | URL to profile picture (optional)             |
| countryCode    | string   | User's country code (optional)                |
| createdAt      | DateTime | Timestamp when the record was created         |
| updatedAt      | DateTime | Timestamp when the record was last updated    |
| deletedAt      | DateTime | Soft-delete timestamp (null if not deleted)   |

#### Relationships

- `user` - Belongs-to relationship with User model

### RefreshToken Model

The RefreshToken model is used to manage refresh tokens for authentication.

#### Fields

| Field Name | Type     | Description                                |
| ---------- | -------- | ------------------------------------------ |
| id         | string   | Primary key, UUID                          |
| userId     | string   | Foreign key to User model                  |
| token      | string   | Unique token string (UUID)                 |
| revoked    | boolean  | Whether the token has been revoked         |
| expiresAt  | DateTime | When the token expires                     |
| createdAt  | DateTime | Timestamp when the record was created      |
| updatedAt  | DateTime | Timestamp when the record was last updated |

#### Methods

| Method           | Description                                            |
| ---------------- | ------------------------------------------------------ |
| createForUser    | Static method to create a new token for a user         |
| findValid        | Static method to find a valid token by token string    |
| revokeAllForUser | Static method to revoke all tokens for a specific user |
| revoke           | Instance method to revoke the current token            |

#### Code Example

```typescript
export default class RefreshToken extends BaseModel {
  @column({ isPrimary: true })
  declare id: string

  @column()
  declare userId: string

  @column()
  declare token: string

  @column()
  declare revoked: boolean

  @column.dateTime()
  declare expiresAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  // Static methods
  static async createForUser(user: User): Promise<RefreshToken> {
    const expiresAt = DateTime.now().plus({ days: 7 })

    return await RefreshToken.create({
      id: randomUUID(),
      userId: user.id,
      token: randomUUID(),
      revoked: false,
      expiresAt,
    })
  }

  static async findValid(token: string): Promise<RefreshToken | null> {
    return await RefreshToken.query()
      .where('token', token)
      .where('revoked', false)
      .where('expires_at', '>', DateTime.now().toSQL())
      .first()
  }

  static async revokeAllForUser(userId: string): Promise<void> {
    await RefreshToken.query().where('user_id', userId).update({ revoked: true })
  }

  // Instance methods
  async revoke(): Promise<void> {
    this.revoked = true
    await this.save()
  }
}
```

## Authentication Validators

Validation for authentication requests is performed using VineJS and defined in `app/features/session/auth_validator.ts`.

### Registration Validator

Validates user registration data:

```typescript
export const createUserValidator = vine.compile(
  vine.object({
    username: vine.string().trim().minLength(3).maxLength(50),
    email: vine.string().trim().email(),
    password: vine.string().minLength(8).confirmed(),
    password_confirmation: vine.string(), // Will be validated by password.confirmed()
  })
)
```

Example request body:

```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "password_confirmation": "securePassword123"
}
```

### Login Validator

Validates login credentials:

```typescript
export const loginValidator = vine.compile(
  vine.object({
    email: vine.string().trim().email(),
    password: vine.string(),
  })
)
```

Example request body:

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

### Refresh Token Validator

Validates refresh token requests:

```typescript
export const refreshTokenValidator = vine.compile(
  vine.object({
    refreshToken: vine.string(),
    rotateRefreshToken: vine.boolean().optional(),
  })
)
```

Example request body:

```json
{
  "refreshToken": "a5c8c1f7-d833-4ded-9d48-8f9e766a1f5b",
  "rotateRefreshToken": true
}
```

## Endpoints

### Registration

**Endpoint:** `POST /api/v1/auth/register`

Creates a new user account.

#### Request

| Field                 | Type   | Required | Description                       |
| --------------------- | ------ | -------- | --------------------------------- |
| username              | string | Yes      | Unique username (3-50 characters) |
| email                 | string | Yes      | Valid email address               |
| password              | string | Yes      | Password (min 8 characters)       |
| password_confirmation | string | Yes      | Must match password               |

#### Response

**Status Code:** 201 (Created)

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "username": "johndoe",
  "email": "<EMAIL>",
  "profile": null
}
```

**Error Responses:**

- 409 Conflict: User with the email already exists
- 422 Unprocessable Entity: Validation errors
- 500 Internal Server Error: Server error during registration

### Login

**Endpoint:** `POST /api/v1/auth/login`

Authenticates a user and returns tokens.

#### Request

| Field    | Type   | Required | Description                      |
| -------- | ------ | -------- | -------------------------------- |
| email    | string | Yes      | Email address for authentication |
| password | string | Yes      | Account password                 |

#### Response

**Status Code:** 200 (OK)

```json
{
  "user": {
    "id": "550e8400-e29b-41d4-a716-************",
    "username": "johndoe",
    "email": "<EMAIL>",
    "createdAt": "2025-05-03T06:28:43.979Z",
    "profile": {
      "firstName": "John",
      "lastName": "Doe",
      "birthDate": "1990-01-01",
      "profilePicture": "https://example.com/profile.jpg",
      "countryCode": "US"
    }
  },
  "token": {
    "type": "bearer",
    "name": null,
    "token": "oat_NjY.NDVLeW5IVkNxcE1ocXJyaURJenhNSUk4T2lBc0dXcUpMajNQbHFaWTEzMzQyNjI1NA",
    "abilities": ["*"],
    "lastUsedAt": null,
    "expiresAt": "2025-05-17T10:17:43.829Z"
  },
  "refreshToken": "a4ac0e3c-aebe-4a72-a036-b5acd89409b3",
  "expiresIn": 900
}
```

**Token Object Fields:**

| Field      | Type     | Description                          |
| ---------- | -------- | ------------------------------------ |
| type       | string   | Always "bearer"                      |
| name       | string   | Usually null                         |
| token      | string   | The actual JWT string                |
| abilities  | string[] | Array of permissions (usually ["*"]) |
| lastUsedAt | string   | Nullable timestamp of last use       |
| expiresAt  | string   | ISO timestamp when the token expires |

**Error Responses:**

- 400 Bad Request: Invalid credentials
- 422 Unprocessable Entity: Validation errors
- 500 Internal Server Error: Server error during login

### Token Refresh

**Endpoint:** `POST /api/v1/auth/refresh`

Refreshes an access token using a valid refresh token.

#### Request

| Field              | Type    | Required | Description                                           |
| ------------------ | ------- | -------- | ----------------------------------------------------- |
| refreshToken       | string  | Yes      | Valid refresh token obtained during login             |
| rotateRefreshToken | boolean | No       | Whether to issue a new refresh token (default: false) |

#### Response with rotateRefreshToken=false

**Status Code:** 200 (OK)

```json
{
  "token": {
    "type": "bearer",
    "name": null,
    "token": "oat_NjY.NDVLeW5IVkNxcE1ocXJyaURJenhNSUk4T2lBc0dXcUpMajNQbHFaWTEzMzQyNjI1NA",
    "abilities": ["*"],
    "lastUsedAt": null,
    "expiresAt": "2025-05-17T10:17:43.829Z"
  },
  "expiresIn": 900
}
```

#### Response with rotateRefreshToken=true

**Status Code:** 200 (OK)

```json
{
  "token": {
    "type": "bearer",
    "name": null,
    "token": "oat_NjY.NDVLeW5IVkNxcE1ocXJyaURJenhNSUk4T2lBc0dXcUpMajNQbHFaWTEzMzQyNjI1NA",
    "abilities": ["*"],
    "lastUsedAt": null,
    "expiresAt": "2025-05-17T10:17:43.829Z"
  },
  "refreshToken": "a4ac0e3c-aebe-4a72-a036-b5acd89409b3",
  "expiresIn": 900
}
```

**Token Object Fields:**

| Field      | Type     | Description                          |
| ---------- | -------- | ------------------------------------ |
| type       | string   | Always "bearer"                      |
| name       | string   | Usually null                         |
| token      | string   | The actual JWT string                |
| abilities  | string[] | Array of permissions (usually ["*"]) |
| lastUsedAt | string   | Nullable timestamp of last use       |
| expiresAt  | string   | ISO timestamp when the token expires |

**Error Responses:**

- 401 Unauthorized: Invalid or expired refresh token
- 422 Unprocessable Entity: Validation errors
- 500 Internal Server Error: Server error during token refresh

### Logout

**Endpoint:** `POST /api/v1/auth/logout`

Invalidates the current access token and optionally revokes the refresh token.

#### Request

| Field        | Type   | Required | Description                        |
| ------------ | ------ | -------- | ---------------------------------- |
| refreshToken | string | No       | Refresh token to revoke (optional) |

#### Response

**Status Code:** 200 (OK)

```json
{
  "message": "Logged out successfully"
}
```

**Error Responses:**

- 401 Unauthorized: Not authenticated
- 500 Internal Server Error: Server error during logout

## Authentication Middleware

The application uses a custom authentication middleware located at `app/middleware/auth_middleware.ts`.

### Implementation Details

- Uses AdonisJS's built-in authentication system
- Handles both API and web requests
- Returns JSON responses for API requests, redirects for web requests
- Default guard is 'api'

```typescript
export default class AuthMiddleware {
  redirectTo = '/login'

  async handle(
    ctx: HttpContext,
    next: NextFn,
    options: {
      guards?: (keyof Authenticators)[]
    } = {}
  ) {
    // For API requests, return JSON response instead of redirecting
    try {
      await ctx.auth.authenticateUsing(options.guards || ['api'], {
        loginRoute: this.redirectTo,
      })
      return next()
    } catch (error) {
      if (ctx.request.accepts(['html', 'json']) === 'json') {
        return ctx.response.unauthorized({ error: 'Authentication required' })
      }

      return ctx.response.redirect(this.redirectTo)
    }
  }
}
```

## Route Configuration

Authentication routes are defined in `start/routes.ts` and are organized into groups:

### Public Routes

These routes don't require authentication:

```typescript
// Group prefix: /api/v1/auth
router.post('register', [SessionController, 'register'])
router.post('login', [SessionController, 'login'])
router.post('refresh', [SessionController, 'refresh'])
```

### Protected Routes

These routes require a valid authentication token:

```typescript
// Group prefix: /api/v1
// All routes use auth middleware (api guard)
router.post('auth/logout', [SessionController, 'logout'])

// Profile routes
router.post('profile', [ProfileController, 'store'])
router.get('profile', [ProfileController, 'show'])
router.patch('profile', [ProfileController, 'update'])
router.delete('profile', [ProfileController, 'destroy'])

// Tasks routes
router.resource('tasks', TasksController).apiOnly()
```

### Additional Middleware

- `throttle`: Rate limiting for all API routes
- `auth`: Authentication for protected routes

## Using Authentication in API Requests

To authenticate API requests after login:

1. Store the `token` received from a successful login
2. Include the token in the Authorization header of subsequent requests:

```
Authorization: Bearer oat_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIxMjM0NTY3ODkwIiwiaWF0IjoxNTE2MjM5MDIyfQ
```

3. To refresh an expired token, use the `/api/v1/auth/refresh` endpoint with the refresh token
4. For logout, call `/api/v1/auth/logout` with the Authorization header

### Token Expiration

- Access tokens expire after 15 minutes
- Refresh tokens expire after 7 days
- For security, implement automatic token refresh logic in your client application
