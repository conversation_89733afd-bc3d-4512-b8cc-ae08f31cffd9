# Database Schema Migration Guide

## Overview

This document outlines the significant changes made to the SkedAI database schema and provides implementation guidance for updating the existing codebase. The changes represent a major architectural shift from a complex supertype/subtype inheritance pattern to a simpler, more performant flattened structure.

## Major Architectural Changes

### 1. Flattened Activity System (Breaking Change)

**Previous Architecture (Supertype/Subtype):**

- `activities` table as parent with common fields
- `tasks`, `appointments`, `events` as child tables with `activityId` as primary key
- Complex JOINs required for every query

**New Architecture (Flattened):**

- `tasks` and `events` are standalone tables with all necessary fields
- `appointments` table **removed entirely**
- No more `activities` table
- Direct, simple queries without JOINs

**Impact:** This is a **breaking change** that requires complete restructuring of models and queries.

### 2. Entity Ownership Change (Breaking Change)

**Previous Ownership:**

- `profiles` table was the primary entity owner
- Most foreign keys referenced `profileId`

**New Ownership:**

- `users` table is now the primary entity owner
- Most foreign keys now reference `userId` (except in `profiles` table itself)

**Impact:** All relationships and foreign keys need to be updated.

### 3. Polymorphic Relationships

**New Pattern:**

- Tables that need to reference multiple activity types now use `activityType` + `activityId` pattern
- Affected tables: `commitments`, `privacy_policy_assignments`, `mentions_to_task`, `activity_tag`

## Detailed Change Analysis

### Tables Removed

#### `activities` Table

**Reason:** Eliminated to flatten the architecture and improve query performance.

**Migration Strategy:**

1. Move all common fields (`title`, `description`, `startDate`, `endDate`, `location`) into individual activity tables
2. Update all foreign key references from `activities.id` to specific table IDs
3. Remove all activity-related JOINs from queries

#### `appointments` Table

**Reason:** Redundant - use `tasks` + `commitments` system for multi-person activities.

**Migration Strategy:**

1. Convert existing appointments to tasks
2. Create commitment records for appointment participants
3. Update any appointment-specific logic to use task + commitment pattern

### Tables Modified (Breaking Changes)

#### `tasks` Table

**Changes:**

- **Added fields:** `title`, `description`, `startDate`, `endDate`, `location` (moved from activities)
- **Changed ownership:** `profileId` → `userId`
- **Removed field:** `activityId` (no longer references activities table)

**Migration Steps:**

```sql
-- 1. Add new fields to tasks table
ALTER TABLE tasks ADD COLUMN title VARCHAR(255) NOT NULL DEFAULT '';
ALTER TABLE tasks ADD COLUMN description TEXT;
ALTER TABLE tasks ADD COLUMN start_date TIMESTAMP;
ALTER TABLE tasks ADD COLUMN end_date TIMESTAMP;
ALTER TABLE tasks ADD COLUMN location JSONB;

-- 2. Change ownership reference
ALTER TABLE tasks DROP CONSTRAINT tasks_profile_id_foreign;
ALTER TABLE tasks ADD COLUMN user_id UUID REFERENCES users(id);

-- 3. Remove activity reference
ALTER TABLE tasks DROP COLUMN activity_id;
ALTER TABLE tasks DROP COLUMN profile_id;
```

#### `events` Table

**Changes:**

- **Added fields:** `title`, `description`, `startDate`, `endDate`, `location` (moved from activities)
- **Changed ownership:** `profileId` → `userId`
- **Removed field:** `activityId` (no longer references activities table)

**Migration Steps:**

```sql
-- Similar to tasks table migration
ALTER TABLE events ADD COLUMN title VARCHAR(255) NOT NULL DEFAULT '';
ALTER TABLE events ADD COLUMN description TEXT;
ALTER TABLE events ADD COLUMN start_date TIMESTAMP;
ALTER TABLE events ADD COLUMN end_date TIMESTAMP;
ALTER TABLE events ADD COLUMN location JSONB;
ALTER TABLE events ADD COLUMN user_id UUID REFERENCES users(id);
ALTER TABLE events DROP COLUMN activity_id;
ALTER TABLE events DROP COLUMN profile_id;
```

#### `commitments` Table

**Changes:**

- **Polymorphic reference:** Now uses `activityType` + `activityId` instead of direct `activityId`
- **Changed ownership:** `hostProfileId`, `inviteeProfileId` → `hostUserId`, `inviteeUserId`

**Migration Steps:**

```sql
-- 1. Add polymorphic fields
ALTER TABLE commitments ADD COLUMN activity_type VARCHAR(20) NOT NULL;
ALTER TABLE commitments ADD COLUMN activity_id UUID NOT NULL;

-- 2. Change ownership
ALTER TABLE commitments ADD COLUMN host_user_id UUID REFERENCES users(id);
ALTER TABLE commitments ADD COLUMN invitee_user_id UUID REFERENCES users(id);

-- 3. Remove old fields
ALTER TABLE commitments DROP COLUMN host_profile_id;
ALTER TABLE commitments DROP COLUMN invitee_profile_id;
```

#### `privacy_policy_assignments` Table

**Changes:**

- **Polymorphic reference:** Now uses `activityType` + `activityId` instead of direct `activityId`

**Migration Steps:**

```sql
ALTER TABLE privacy_policy_assignments ADD COLUMN activity_type VARCHAR(20) NOT NULL;
-- activity_id column already exists, just needs to be repurposed
```

### Tables with Ownership Changes Only

The following tables need `profileId` → `userId` changes but no structural modifications:

- `policy_categories`
- `privacy_policy_templates`
- `mentions_to_task`
- `user_contact`
- `user_group`
- `user_contact_list`
- `user_availability_slot`
- `tag`
- `activity_tag`

**Migration Pattern:**

```sql
-- Example for policy_categories
ALTER TABLE policy_categories ADD COLUMN user_id UUID REFERENCES users(id);
-- Populate user_id from profile_id via join
UPDATE policy_categories SET user_id = (
  SELECT u.id FROM users u
  JOIN profiles p ON u.auth_user_id = p.user_id
  WHERE p.id = policy_categories.profile_id
);
ALTER TABLE policy_categories DROP COLUMN profile_id;
```

## Implementation Strategy

### Phase 1: Database Schema Updates

1. **Create Migration Scripts**

   ```bash
   # Create new migration
   node ace make:migration flatten_activity_system
   ```

2. **Update Migration Files**

   - Remove `activities` and `appointments` tables
   - Add fields to `tasks` and `events` tables
   - Update all foreign key references
   - Add polymorphic fields where needed

3. **Update Indexes**

   ```sql
   -- Remove old indexes
   DROP INDEX idx_activities_profile_id;
   DROP INDEX idx_tasks_activity_id;

   -- Add new indexes
   CREATE INDEX idx_tasks_user_id ON tasks(user_id);
   CREATE INDEX idx_events_user_id ON events(user_id);
   CREATE INDEX idx_commitments_activity ON commitments(activity_type, activity_id);
   ```

### Phase 2: Model Updates

#### Update Task Model

```typescript
// app/models/Task.ts
export default class Task extends BaseModel {
  @column({ isPrimary: true })
  declare id: string

  @column()
  declare userId: string // Changed from profileId

  @column()
  declare title: string // Added from activities

  @column()
  declare description: string | null // Added from activities

  @column.dateTime()
  declare startDate: DateTime | null // Added from activities

  @column.dateTime()
  declare endDate: DateTime | null // Added from activities

  @column()
  declare location: any | null // Added from activities (JSONB)

  // Remove activity relationship
  // @belongsTo(() => Activity) - REMOVE THIS

  @belongsTo(() => User) // Changed from Profile
  declare user: BelongsTo<typeof User>

  // Add polymorphic relationships
  @hasMany(() => Commitment, {
    foreignKey: 'activityId',
    onQuery: (query) => query.where('activityType', 'task'),
  })
  declare commitments: HasMany<typeof Commitment>
}
```

#### Update Event Model

```typescript
// app/models/Event.ts
export default class Event extends BaseModel {
  @column({ isPrimary: true })
  declare id: string

  @column()
  declare userId: string // Changed from profileId

  @column()
  declare title: string // Added from activities

  @column()
  declare description: string | null // Added from activities

  @column.dateTime()
  declare startDate: DateTime | null // Added from activities

  @column.dateTime()
  declare endDate: DateTime | null // Added from activities

  @column()
  declare location: any | null // Added from activities

  @belongsTo(() => User) // Changed from Profile
  declare user: BelongsTo<typeof User>

  @hasMany(() => Commitment, {
    foreignKey: 'activityId',
    onQuery: (query) => query.where('activityType', 'event'),
  })
  declare commitments: HasMany<typeof Commitment>
}
```

#### Remove Activity Model

```bash
# Delete the file
rm app/models/Activity.ts
```

#### Update Commitment Model

```typescript
// app/models/Commitment.ts
export default class Commitment extends BaseModel {
  @column()
  declare activityType: 'task' | 'event' // New polymorphic field

  @column()
  declare activityId: string // Repurposed for polymorphic reference

  @column()
  declare hostUserId: string // Changed from hostProfileId

  @column()
  declare inviteeUserId: string // Changed from inviteeProfileId

  @belongsTo(() => User, { foreignKey: 'hostUserId' })
  declare hostUser: BelongsTo<typeof User>

  @belongsTo(() => User, { foreignKey: 'inviteeUserId' })
  declare inviteeUser: BelongsTo<typeof User>

  // Polymorphic relationships
  public async getActivity() {
    if (this.activityType === 'task') {
      return await Task.find(this.activityId)
    } else if (this.activityType === 'event') {
      return await Event.find(this.activityId)
    }
    return null
  }
}
```

### Phase 3: Service Layer Updates

#### Update Task Service

```typescript
// app/services/TaskService.ts
export default class TaskService {
  async createTask(userId: string, taskData: any) {
    // No longer need to create activity first
    const task = await Task.create({
      userId, // Changed from profileId
      title: taskData.title,
      description: taskData.description,
      startDate: taskData.startDate,
      endDate: taskData.endDate,
      location: taskData.location,
      status: taskData.status,
      priority: taskData.priority,
      // ... other task-specific fields
    })

    return task
  }

  async getTasks(userId: string, filters?: any) {
    // Much simpler query - no JOINs needed
    return await Task.query()
      .where('userId', userId) // Changed from profileId
      .if(filters?.status, (query) => query.where('status', filters.status))
      .if(filters?.startDate, (query) => query.where('startDate', '>=', filters.startDate))
      .orderBy('createdAt', 'desc')
  }
}
```

#### Update Privacy Policy Service

```typescript
// app/services/PrivacyPolicyService.ts
export default class PrivacyPolicyService {
  async assignPolicyToActivity(
    activityType: 'task' | 'event',
    activityId: string,
    templateId: string
  ) {
    return await PrivacyPolicyAssignment.create({
      activityType, // New polymorphic field
      activityId, // Repurposed for polymorphic reference
      templateId,
    })
  }

  async getActivityPolicy(activityType: 'task' | 'event', activityId: string) {
    return await PrivacyPolicyAssignment.query()
      .where('activityType', activityType)
      .where('activityId', activityId)
      .preload('template', (query) => query.preload('rules'))
      .first()
  }
}
```

### Phase 4: Controller Updates

#### Update Tasks Controller

```typescript
// app/controllers/TasksController.ts
export default class TasksController {
  async store({ auth, request, response }: HttpContext) {
    const userId = auth.user!.id // Changed from profile.id
    const taskData = request.only([
      'title',
      'description',
      'startDate',
      'endDate',
      'location',
      'status',
      'priority',
    ])

    const task = await this.taskService.createTask(userId, taskData)
    return response.created(task)
  }

  async index({ auth, request, response }: HttpContext) {
    const userId = auth.user!.id // Changed from profile.id
    const filters = request.qs()

    const tasks = await this.taskService.getTasks(userId, filters)
    return response.ok(tasks)
  }
}
```

### Phase 5: Route Updates

Most routes remain the same, but ensure authentication middleware provides `user` instead of `profile`:

```typescript
// start/routes.ts
router
  .group(() => {
    router.resource('tasks', TasksController).apiOnly()
    router.resource('events', EventsController).apiOnly()
    // Remove appointments routes
  })
  .prefix('/api/v1')
  .middleware(middleware.auth())
```

## Testing Strategy

### 1. Unit Tests

```typescript
// tests/unit/models/task.spec.ts
test('task belongs to user', async ({ assert }) => {
  const user = await User.create(userData)
  const task = await Task.create({ userId: user.id, title: 'Test Task' })

  await task.load('user')
  assert.equal(task.user.id, user.id)
})

test('task can have commitments', async ({ assert }) => {
  const task = await Task.create(taskData)
  const commitment = await Commitment.create({
    activityType: 'task',
    activityId: task.id,
    hostUserId: user1.id,
    inviteeUserId: user2.id,
  })

  await task.load('commitments')
  assert.lengthOf(task.commitments, 1)
})
```

### 2. Integration Tests

```typescript
// tests/functional/tasks.spec.ts
test('create task without activity table', async ({ client, assert }) => {
  const response = await client
    .post('/api/v1/tasks')
    .json({
      title: 'Test Task',
      description: 'Test Description',
      startDate: '2024-03-20T10:00:00Z',
    })
    .loginAs(user)

  response.assertStatus(201)

  // Verify task was created directly (no activity table)
  const task = await Task.find(response.body().id)
  assert.equal(task!.title, 'Test Task')
  assert.equal(task!.userId, user.id)
})
```

## Rollback Strategy

If issues arise, the rollback process involves:

1. **Restore Previous Migration State**

   ```bash
   node ace migration:rollback --batch=1
   ```

2. **Restore Model Files**

   - Restore `Activity` model
   - Restore `Appointment` model
   - Revert `Task` and `Event` models to previous state

3. **Restore Service Logic**
   - Restore JOIN-based queries
   - Restore activity creation logic

## Performance Benefits

The new flattened structure provides:

1. **Simpler Queries:** No JOINs required for basic operations
2. **Better Performance:** Direct table access instead of inheritance queries
3. **Easier Maintenance:** Clearer data model without complex relationships
4. **Improved Scalability:** Better query optimization opportunities

## Breaking Changes Summary

1. **`activities` table removed** - All activity data moved to specific tables
2. **`appointments` table removed** - Use tasks + commitments instead
3. **Entity ownership changed** - `profileId` → `userId` throughout system
4. **Polymorphic relationships** - New `activityType` + `activityId` pattern
5. **Model relationships updated** - All foreign keys and relationships changed
6. **Query patterns changed** - No more JOINs for basic activity operations

## Current Implementation Status

Based on the existing migrations in `database/migrations/`, the following tables are **currently implemented** with the old architecture:

### Implemented Tables (Need Migration)

- ✅ `auth_users`, `users`, `profiles`, `profile_settings` - Core user system
- ✅ `auth_access_tokens`, `refresh_tokens` - Authentication tokens
- ✅ `activities` - **NEEDS REMOVAL** (supertype table)
- ✅ `tasks` - **NEEDS RESTRUCTURING** (currently references activities)
- ✅ `events` - **NEEDS RESTRUCTURING** (currently references activities)
- ✅ `appointments` - **NEEDS REMOVAL** (redundant with tasks + commitments)
- ✅ Privacy policy system - **NEEDS OWNERSHIP CHANGE** (profileId → userId)

### Not Yet Implemented (Future Features)

- ⏳ `commitments` - Multi-person activity participation
- ⏳ `activity_log` - Activity change tracking
- ⏳ Contact and group management tables
- ⏳ User availability system
- ⏳ Tagging system

## Migration Priority

### Phase 1: Critical Breaking Changes (High Priority)

1. **Remove `activities` table** and flatten the structure
2. **Remove `appointments` table**
3. **Update `tasks` and `events`** to be standalone
4. **Change entity ownership** from `profileId` to `userId`

### Phase 2: Model and Service Updates (High Priority)

1. **Update all model files** to reflect new structure
2. **Refactor service layer** to remove JOIN-based queries
3. **Update controllers** for new entity ownership

### Phase 3: New Feature Implementation (Medium Priority)

1. **Implement `commitments` table** for multi-person activities
2. **Add polymorphic relationships** for cross-references
3. **Implement remaining planned tables** as needed

## Migration Command Sequence

```bash
# 1. Create the main migration for flattening
node ace make:migration flatten_activity_system_and_change_ownership

# 2. Update models after migration
# (Update all model files manually)

# 3. Create migration for new features (when ready)
node ace make:migration add_commitments_and_polymorphic_relationships

# 4. Run migrations
node ace migration:run

# 5. Run tests to verify
npm test
```

## Next Steps

1. **Create and run migrations** to update database schema (Priority: HIGH)
2. **Update all model files** according to the new structure (Priority: HIGH)
3. **Refactor service layer** to use new query patterns (Priority: HIGH)
4. **Update controllers** to use new entity ownership (Priority: HIGH)
5. **Run comprehensive tests** to ensure functionality is preserved (Priority: HIGH)
6. **Update API documentation** to reflect new data structures (Priority: MEDIUM)
7. **Implement new features** like commitments system (Priority: MEDIUM)

This migration represents a significant architectural improvement that will make the codebase more maintainable and performant while preserving all existing functionality.
