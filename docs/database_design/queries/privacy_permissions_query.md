# SkedAI Optimized Query Implementation

This document contains the optimized implementation of the availability and policy system, designed to minimize database queries while maintaining the sophisticated privacy controls. The optimization achieves **2-3 queries per request** (2 on Redis cache hit, 3 on cache miss).

## Optimization Strategy Overview

### Key Optimizations

1. **Redis Caching**: Cache relationship context data with 15-minute TTL
2. **Single CTE Query**: Use Common Table Expressions to fetch all relationship data in one query
3. **Smart Preloading**: Load activities with all policy data in a single optimized query
4. **In-Memory Processing**: Process policy rules in application memory rather than complex SQL
5. **Event-Driven Cache Invalidation**: Clear cache when relationships change

### Performance Target

- **Cache Hit**: 2 queries (activities + availability slots)
- **Cache Miss**: 3 queries (+ relationship context query)
- **No Database Schema Changes Required**

## General Query Process

Here is a high-level breakdown of how the optimized availability query works:

1.  **Fetch Requester Context (Cached)**: The system first attempts to retrieve the requesting user's relationship context (contacts, shared groups, etc.) from the Redis cache. If the context is not in the cache or has expired, it is fetched from the database using a single, efficient CTE query and then stored in the cache for future requests.
2.  **Fetch Activities with Policy Data**: In parallel, the system queries the `activities` table for the target user on the specified date. This query uses eager loading to bring back all associated `policyAssignment`, `template`, and `rules` data in a single database call.
3.  **Fetch Availability Slots**: Also in parallel, the system queries the `user_availability_slot` table for the target user on the specified date, fetching both recurring and specific slots.
4.  **In-Memory Policy Evaluation**: Once the activity and availability data, along with the requester context, are loaded into application memory, the policy rules for each activity are evaluated without needing further database queries.
5.  **Filter and Format Results**: Based on the policy evaluation, activities are filtered for visibility, and the remaining data (title, description, location) is transformed according to the viewer's allowed detail level. The final filtered list of activities and general availability slots is returned to the requester.

This process minimizes database round trips by leveraging caching and fetching all necessary data for policy evaluation upfront, then performing the rule application efficiently in memory.

### Query Flow Diagram

```mermaid
flowchart TD
    A["Request: GET /availability/(profileId)/(date)"] --> B[Step 1: Fetch Requester Context]

    B --> C{Redis Cache Hit?}
    C -->|Yes - Cache Hit| D[Return Cached Context<br/>2 queries total]
    C -->|No - Cache Miss| E[Execute CTE Query<br/>3 queries total]

    E --> F[Cache Result<br/>TTL: 15 minutes]
    F --> G[Continue with Context]
    D --> G

    G --> H[Step 2 & 3: Parallel Data Fetching]

    H --> I[Query 1: Activities + Policy Data<br/>WITH eager loading:<br/>• policyAssignment<br/>• template<br/>• rules ordered by priority]
    H --> J[Query 2: Availability Slots<br/>• Recurring slots by day<br/>• Specific date slots]

    I --> K[Step 4: In-Memory Policy Evaluation]
    J --> K

    K --> L[For each activity:<br/>1. Check policy template<br/>2. Find matching rule by priority<br/>3. Apply visibility settings<br/>4. Transform data]

    L --> M[Step 5: Filter & Format Results]
    M --> N[Response with Performance Metrics]

    style C fill:#e1f5fe,color:#000
    style D fill:#c8e6c9,color:#000
    style E fill:#ffcdd2,color:#000
    style K fill:#fff3e0,color:#000
```

### Cache Strategy Diagram

```mermaid
graph TB
    subgraph "Cache Key Pattern"
        A["rel_ctx:(requesterProfileId):(targetProfileId)"]
        B[TTL: 15 minutes]
    end

    subgraph "User Requests"
        C[User A requests<br/>User X availability]
        D[User B requests<br/>User X availability]
        E[User C requests<br/>User Y availability]
    end

    subgraph "Cache Entries"
        F[rel_ctx:A:X]
        G[rel_ctx:B:X]
        H[rel_ctx:C:Y]
    end

    subgraph "Cache Invalidation Triggers"
        I[UserContact.afterSave<br/>when status = accepted]
        J[UserContact.afterDelete<br/>when relationship deleted]
        K[GroupMember.afterSave<br/>when user joins/leaves group]
        L[GroupMember.afterDelete<br/>when membership removed]
    end

    subgraph "Invalidation Strategy"
        M[Bidirectional Clear<br/>rel_ctx:A:B AND rel_ctx:B:A]
        N[User-wide Clear<br/>All cache entries for user]
    end

    C --> F
    D --> G
    E --> H

    I --> M
    J --> M
    K --> N
    L --> N

    style A fill:#e3f2fd,color:#000
    style I fill:#fff3e0,color:#000
    style J fill:#fff3e0,color:#000
    style K fill:#fff3e0,color:#000
    style L fill:#fff3e0,color:#000
```

### Performance Comparison

```mermaid
graph TD
    subgraph "Original Implementation"
        A1[Activities Query] --> A2[Policy Templates<br/>N+1 Problem]
        A2 --> A3[Policy Rules<br/>N+1 Problem]
        A3 --> A4[Contact Relationship]
        A4 --> A5[Shared Groups]
        A5 --> A6[Shared Contact Lists]
        A6 --> A7[Availability Slots]
        A7 --> A8[Total: 7+ queries<br/>10-15 in practice]
    end

    A8 --> D1[Performance Improvement<br/>70-85% Reduction<br/>in Database Queries]

    subgraph "Optimized Implementation - Cache Hit"
        B1[Activities + Policy Data<br/>Eager Loading] --> B2[Availability Slots]
        B2 --> B3[Total: 2 queries]
    end

    subgraph "Optimized Implementation - Cache Miss"
        C1[Relationship Context<br/>Single CTE Query] --> C2[Activities + Policy Data<br/>Eager Loading]
        C2 --> C3[Availability Slots]
        C3 --> C4[Total: 3 queries]
    end

    B3 --> D1
    C4 --> D1

    style A8 fill:#ffcdd2,color:#000
    style B3 fill:#c8e6c9,color:#000
    style C4 fill:#c8e6c9,color:#000
    style D1 fill:#e8f5e8,color:#000
```

### Database Query Flow

```mermaid
sequenceDiagram
    participant Client
    participant Controller
    participant Service
    participant Redis
    participant Database

    Client->>Controller: GET /availability/(profileId)/(date)
    Controller->>Service: getAvailabilityForDay()

    Note over Service: Step 1: Get Requester Context
    Service->>Redis: GET rel_ctx:(requester):(target)

    alt Cache Hit
        Redis-->>Service: Return cached context
        Note over Service: 2 queries total
    else Cache Miss
        Redis-->>Service: null
        Service->>Database: CTE Query (contacts, groups, lists)
        Database-->>Service: Relationship context
        Service->>Redis: SETEX rel_ctx (15 min TTL)
        Note over Service: 3 queries total
    end

    Note over Service: Step 2 & 3: Parallel Queries
    par Activities Query
        Service->>Database: Activities + Policy Data (eager loading)
        Database-->>Service: Activities with templates & rules
    and Availability Query
        Service->>Database: User availability slots
        Database-->>Service: Availability slots
    end

    Note over Service: Step 4: In-Memory Processing
    Service->>Service: Evaluate policy rules for each activity
    Service->>Service: Transform data based on visibility levels

    Service-->>Controller: Filtered availability data
    Controller-->>Client: JSON response with performance metrics
```

## Complete Optimized Implementation

### Enhanced Service Class

```typescript
// app/services/OptimizedAvailabilityService.ts
import { DateTime } from 'luxon'
import Redis from '@ioc:Adonis/Addons/Redis'
import Activity from '#models/Activity'
import UserAvailabilitySlot from '#models/UserAvailabilitySlot'
import Database from '@ioc:Adonis/Lucid/Database'

/**
 * OptimizedAvailabilityService implements a high-performance version of the
 * availability and policy system using Redis caching and optimized queries.
 *
 * Performance Characteristics:
 * - Cache Hit: 2 SQL queries
 * - Cache Miss: 3 SQL queries
 * - No database schema changes required
 */
export default class OptimizedAvailabilityService {
  /**
   * Main optimized method for getting availability with minimal database queries.
   * Uses Redis caching for relationship context and optimized SQL for data fetching.
   */
  async getAvailabilityForDay(requesterProfileId: string, targetProfileId: string, date: DateTime) {
    // Step 1: Get relationship context (cached or fresh)
    const requesterContext = await this.getCachedRequesterContext(
      requesterProfileId,
      targetProfileId
    )

    // Step 2: Get activities with all policy data in one optimized query
    const activities = await this.getActivitiesWithPolicyData(targetProfileId, date)

    // Step 3: Get availability slots (can run in parallel with activities)
    const availabilitySlots = await this.getAvailabilitySlots(targetProfileId, date)

    // Step 4: Process policy rules in memory (no additional queries)
    const visibleActivities = this.processActivitiesInMemory(activities, requesterContext)

    return {
      date: date.toISODate(),
      targetProfileId,
      generalAvailability: availabilitySlots,
      activities: visibleActivities,
      requesterContext,
      cacheHit: requesterContext.fromCache,
    }
  }

  /**
   * Gets relationship context with Redis caching.
   * Cache key: "rel_ctx:(requesterProfileId):(targetProfileId)"
   * TTL: 15 minutes (900 seconds)
   */
  private async getCachedRequesterContext(requesterProfileId: string, targetProfileId: string) {
    const cacheKey = `rel_ctx:${requesterProfileId}:${targetProfileId}`

    // Try to get from cache first
    const cached = await Redis.get(cacheKey)
    if (cached) {
      const context = JSON.parse(cached)
      context.fromCache = true
      return context
    }

    // Cache miss - fetch from database using optimized single query
    const context = await this.fetchRequesterContextOptimized(requesterProfileId, targetProfileId)

    // Cache the result
    await Redis.setex(cacheKey, 900, JSON.stringify(context)) // 15 minutes TTL

    context.fromCache = false
    return context
  }

  /**
   * Optimized single-query approach to fetch all relationship context data.
   * Uses CTEs (Common Table Expressions) to gather all relationship data efficiently.
   */
  private async fetchRequesterContextOptimized(
    requesterProfileId: string,
    targetProfileId: string
  ) {
    const result = await Database.rawQuery(
      `
      WITH 
      -- Check for contact relationship (bidirectional)
      contact_check AS (
        SELECT 1 as is_contact
        FROM user_contacts 
        WHERE status = 'accepted' 
        AND (
          (requester_profile_id = ? AND addressee_profile_id = ?) OR
          (requester_profile_id = ? AND addressee_profile_id = ?)
        )
        LIMIT 1
      ),
      -- Get shared groups
      shared_groups AS (
        SELECT gm1.group_id
        FROM group_members gm1
        INNER JOIN group_members gm2 ON gm1.group_id = gm2.group_id
        WHERE gm1.profile_id = ? 
        AND gm2.profile_id = ?
        AND gm1.deleted_at IS NULL 
        AND gm2.deleted_at IS NULL
      ),
      -- Get shared contact lists
      shared_contact_lists AS (
        SELECT clm1.list_id
        FROM contact_list_members clm1
        INNER JOIN contact_list_members clm2 ON clm1.list_id = clm2.list_id
        WHERE clm1.profile_id = ? 
        AND clm2.profile_id = ?
        AND clm1.deleted_at IS NULL 
        AND clm2.deleted_at IS NULL
      )
      -- Combine all results
      SELECT 
        COALESCE((SELECT is_contact FROM contact_check), 0) as is_contact,
        COALESCE(
          (SELECT json_agg(group_id) FROM shared_groups), 
          '[]'::json
        ) as shared_group_ids,
        COALESCE(
          (SELECT json_agg(list_id) FROM shared_contact_lists), 
          '[]'::json
        ) as shared_contact_list_ids
    `,
      [
        requesterProfileId,
        targetProfileId, // contact check params
        targetProfileId,
        requesterProfileId, // contact check params (reverse)
        requesterProfileId,
        targetProfileId, // shared groups params
        requesterProfileId,
        targetProfileId, // shared contact lists params
      ]
    )

    const row = result.rows[0]
    return {
      isContact: !!row.is_contact,
      sharedGroupIds: row.shared_group_ids || [],
      sharedContactListIds: row.shared_contact_list_ids || [],
      requesterProfileId,
    }
  }

  /**
   * Optimized query to get activities with all policy data preloaded.
   * Uses strategic eager loading to minimize queries.
   */
  private async getActivitiesWithPolicyData(targetProfileId: string, date: DateTime) {
    return await Activity.query()
      .where('profileId', targetProfileId)
      .whereRaw('DATE(start_date) = ?', [date.toISODate()])
      .preload('policyAssignment', (assignmentQuery) => {
        assignmentQuery.preload('template', (templateQuery) => {
          templateQuery.preload('rules', (rulesQuery) => {
            // Order rules by priority for efficient processing
            rulesQuery.orderBy('priority', 'desc')
          })
        })
      })
      .orderBy('startDate', 'asc')
      .exec()
  }

  /**
   * Get availability slots for the target user and date.
   * This query is separate and can run in parallel with activities.
   */
  private async getAvailabilitySlots(targetProfileId: string, date: DateTime) {
    return await UserAvailabilitySlot.query()
      .where('profileId', targetProfileId)
      .where((query) => {
        // Recurring slots for this day of week
        query.where('dayOfWeek', date.weekday).whereNull('specificStartDate')
        // OR specific slots for this exact date
        query.orWhere((subQuery) => {
          subQuery
            .whereRaw('DATE(specific_start_date) = ?', [date.toISODate()])
            .whereNotNull('specificStartDate')
        })
      })
      .orderBy('startTime', 'asc')
      .exec()
  }

  /**
   * Process all policy rules in memory to avoid additional database queries.
   * This is efficient because we've preloaded all necessary data.
   */
  private processActivitiesInMemory(activities: any[], requesterContext: any) {
    const visibleActivities = []

    for (const activity of activities) {
      const visibilityResult = this.evaluateActivityVisibilityInMemory(activity, requesterContext)

      if (visibilityResult.isVisible) {
        visibleActivities.push({
          id: activity.id,
          startDate: activity.startDate,
          endDate: activity.endDate,
          title: this.getVisibleTitle(activity, visibilityResult.detailLevel),
          description: this.getVisibleDescription(activity, visibilityResult.detailLevel),
          location: this.getVisibleLocation(activity, visibilityResult.detailLevel),
          blocksScheduling: visibilityResult.blocksScheduling,
          customMessage: visibilityResult.customMessage,
          detailLevel: visibilityResult.detailLevel,
        })
      }
    }

    return visibleActivities
  }

  /**
   * In-memory policy evaluation using preloaded data.
   * No database queries needed since all data is already loaded.
   */
  private evaluateActivityVisibilityInMemory(activity: any, requesterContext: any) {
    // Default behavior if no policy assignment
    if (!activity.policyAssignment || !activity.policyAssignment.template) {
      return {
        isVisible: true,
        detailLevel: 'busy_only',
        blocksScheduling: true,
        customMessage: null,
      }
    }

    const template = activity.policyAssignment.template
    const assignment = activity.policyAssignment
    const rules = template.rules || []

    // Rules are already sorted by priority (desc) from the query
    for (const rule of rules) {
      if (this.doesRuleApplyToRequesterInMemory(rule, requesterContext)) {
        return {
          isVisible: rule.detailVisibility !== 'hidden',
          detailLevel: rule.detailVisibility,
          blocksScheduling: rule.blocksScheduling,
          customMessage: rule.customMessage,
        }
      }
    }

    // No rule matched, use template defaults with assignment overrides
    const detailLevel = assignment.overrideDetailVisibility || template.defaultDetailVisibility
    const blocksScheduling = assignment.overrideBlocksScheduling ?? template.blocksScheduling
    const customMessage = assignment.overrideCustomMessage || template.defaultCustomMessage

    return {
      isVisible: detailLevel !== 'hidden',
      detailLevel,
      blocksScheduling,
      customMessage,
    }
  }

  /**
   * In-memory rule matching using preloaded context data.
   */
  private doesRuleApplyToRequesterInMemory(rule: any, requesterContext: any): boolean {
    switch (rule.viewerScopeType) {
      case 'public':
        return true

      case 'all_contacts':
        return requesterContext.isContact

      case 'specific_contact':
        return (
          requesterContext.isContact && rule.viewerTargetUserId === requesterContext.requesterUserId
        )

      case 'specific_group':
        return requesterContext.sharedGroupIds.includes(rule.viewerTargetGroupId)

      case 'specific_contact_list':
        return requesterContext.sharedContactListIds.includes(rule.viewerTargetContactListId)

      default:
        return false
    }
  }

  /**
   * Cache invalidation methods - called when relationships change
   */
  async invalidateRelationshipCache(profileId1: string, profileId2: string) {
    const keys = [`rel_ctx:${profileId1}:${profileId2}`, `rel_ctx:${profileId2}:${profileId1}`]
    await Redis.del(...keys)
  }

  async invalidateUserRelationshipCache(profileId: string) {
    // Get all cache keys for this user (requires Redis SCAN)
    const pattern = `rel_ctx:${profileId}:*`
    const keys = await Redis.keys(pattern)

    const reversePattern = `rel_ctx:*:${profileId}`
    const reverseKeys = await Redis.keys(reversePattern)

    const allKeys = [...keys, ...reverseKeys]
    if (allKeys.length > 0) {
      await Redis.del(...allKeys)
    }
  }

  // Visibility helper methods (same as original implementation)
  private getVisibleTitle(activity: any, detailLevel: string): string | null {
    switch (detailLevel) {
      case 'hidden':
        return null
      case 'busy_only':
        return 'Busy'
      case 'title_only':
      case 'full_details':
        return activity.title
      default:
        return 'Busy'
    }
  }

  private getVisibleDescription(activity: any, detailLevel: string): string | null {
    return detailLevel === 'full_details' ? activity.description : null
  }

  private getVisibleLocation(activity: any, detailLevel: string): any | null {
    return detailLevel === 'full_details' ? activity.location : null
  }
}
```

### Cache Invalidation via Model Hooks

```typescript
// app/models/UserContact.ts
import { BaseModel, beforeSave, afterSave, afterDelete } from '@ioc:Adonis/Lucid/Orm'
import OptimizedAvailabilityService from '#services/OptimizedAvailabilityService'

export default class UserContact extends BaseModel {
  // ... existing model definition ...

  @afterSave()
  public static async invalidateContactCache(contact: UserContact) {
    if (contact.$dirty.status && contact.status === 'accepted') {
      const service = new OptimizedAvailabilityService()
      await service.invalidateRelationshipCache(
        contact.requesterProfileId,
        contact.addresseeProfileId
      )
    }
  }

  @afterDelete()
  public static async invalidateContactCacheOnDelete(contact: UserContact) {
    const service = new OptimizedAvailabilityService()
    await service.invalidateRelationshipCache(
      contact.requesterProfileId,
      contact.addresseeProfileId
    )
  }
}
```

```typescript
// app/models/GroupMember.ts
import { BaseModel, afterSave, afterDelete } from '@ioc:Adonis/Lucid/Orm'
import OptimizedAvailabilityService from '#services/OptimizedAvailabilityService'

export default class GroupMember extends BaseModel {
  // ... existing model definition ...

  @afterSave()
  @afterDelete()
  public static async invalidateGroupMemberCache(member: GroupMember) {
    const service = new OptimizedAvailabilityService()
    await service.invalidateUserRelationshipCache(member.profileId)
  }
}
```

### Enhanced Controller with Performance Monitoring

```typescript
// app/controllers/OptimizedAvailabilityController.ts
import { HttpContext } from '@adonisjs/core/http'
import { DateTime } from 'luxon'
import OptimizedAvailabilityService from '#services/OptimizedAvailabilityService'

export default class OptimizedAvailabilityController {
  async getAvailability({ params, auth, response }: HttpContext) {
    const startTime = Date.now()

    try {
      const requesterProfileId = auth.user!.profile.id
      const targetProfileId = params.profileId

      const date = DateTime.fromISO(params.date)
      if (!date.isValid) {
        return response.badRequest({
          message: 'Invalid date format. Use YYYY-MM-DD format.',
        })
      }

      const availabilityService = new OptimizedAvailabilityService()
      const availability = await availabilityService.getAvailabilityForDay(
        requesterProfileId,
        targetProfileId,
        date
      )

      const responseTime = Date.now() - startTime

      return response.ok({
        ...availability,
        performance: {
          responseTimeMs: responseTime,
          cacheHit: availability.cacheHit,
          estimatedQueries: availability.cacheHit ? 2 : 3,
        },
      })
    } catch (error) {
      console.error('Optimized availability fetch error:', error)
      return response.badRequest({
        message: 'Failed to fetch availability information',
      })
    }
  }

  /**
   * Batch endpoint for multiple days - leverages caching for efficiency
   */
  async getAvailabilityRange({ params, auth, response }: HttpContext) {
    const startTime = Date.now()

    try {
      const requesterProfileId = auth.user!.profile.id
      const targetProfileId = params.profileId

      const startDate = DateTime.fromISO(params.startDate)
      const endDate = DateTime.fromISO(params.endDate)

      if (!startDate.isValid || !endDate.isValid) {
        return response.badRequest({
          message: 'Invalid date format. Use YYYY-MM-DD format.',
        })
      }

      const daysDiff = endDate.diff(startDate, 'days').days
      if (daysDiff > 31) {
        return response.badRequest({
          message: 'Date range cannot exceed 31 days.',
        })
      }

      const availabilityService = new OptimizedAvailabilityService()
      const results = []
      let totalQueries = 0
      let cacheHits = 0

      // Process each day - relationship context will be cached after first request
      let currentDate = startDate
      while (currentDate <= endDate) {
        const dayAvailability = await availabilityService.getAvailabilityForDay(
          requesterProfileId,
          targetProfileId,
          currentDate
        )

        results.push(dayAvailability)
        totalQueries += dayAvailability.cacheHit ? 2 : 3
        if (dayAvailability.cacheHit) cacheHits++

        currentDate = currentDate.plus({ days: 1 })
      }

      const responseTime = Date.now() - startTime

      return response.ok({
        startDate: startDate.toISODate(),
        endDate: endDate.toISODate(),
        targetProfileId,
        days: results,
        performance: {
          responseTimeMs: responseTime,
          totalDays: results.length,
          totalEstimatedQueries: totalQueries,
          cacheHitRate: `${cacheHits}/${results.length}`,
          avgQueriesPerDay: totalQueries / results.length,
        },
      })
    } catch (error) {
      console.error('Optimized availability range fetch error:', error)
      return response.badRequest({
        message: 'Failed to fetch availability range',
      })
    }
  }
}
```

## Required Database Indexes

```sql
-- Indexes for optimal query performance (no table structure changes)

-- Activity queries
CREATE INDEX IF NOT EXISTS idx_activity_profile_date ON activities(profile_id, start_date);
CREATE INDEX IF NOT EXISTS idx_activity_date ON activities(start_date);

-- Policy system indexes
CREATE INDEX IF NOT EXISTS idx_policy_assignment_activity ON privacy_policy_assignments(activity_id);
CREATE INDEX IF NOT EXISTS idx_policy_assignment_template ON privacy_policy_assignments(template_id);
CREATE INDEX IF NOT EXISTS idx_policy_rules_template ON policy_template_rules(template_id);
CREATE INDEX IF NOT EXISTS idx_policy_rules_priority ON policy_template_rules(template_id, priority DESC);

-- Relationship context indexes
CREATE INDEX IF NOT EXISTS idx_user_contact_requester ON user_contacts(requester_profile_id, status);
CREATE INDEX IF NOT EXISTS idx_user_contact_addressee ON user_contacts(addressee_profile_id, status);
CREATE INDEX IF NOT EXISTS idx_group_member_profile ON group_members(profile_id);
CREATE INDEX IF NOT EXISTS idx_group_member_group ON group_members(group_id);
CREATE INDEX IF NOT EXISTS idx_contact_list_member_profile ON contact_list_members(profile_id);
CREATE INDEX IF NOT EXISTS idx_contact_list_member_list ON contact_list_members(list_id);

-- Availability slots
CREATE INDEX IF NOT EXISTS idx_availability_profile_dow ON user_availability_slots(profile_id, day_of_week);
CREATE INDEX IF NOT EXISTS idx_availability_profile_specific ON user_availability_slots(profile_id, specific_start_date);
```

## Redis Configuration

```typescript
// config/redis.ts
export default {
  connection: 'local',
  connections: {
    local: {
      host: Env.get('REDIS_HOST', '127.0.0.1'),
      port: Env.get('REDIS_PORT', 6379),
      password: Env.get('REDIS_PASSWORD', ''),
      db: 0,
      keyPrefix: 'skedai:',
      retryDelayOnFailover: 100,
      retryDelayOnClusterDown: 300,
      maxRetriesPerRequest: 3,
    },
  },
}
```

## Performance Monitoring

```typescript
// app/middleware/PerformanceMonitor.ts
export default class PerformanceMonitor {
  public async handle({ request, response }: HttpContext, next: () => Promise<void>) {
    const startTime = Date.now()

    await next()

    const responseTime = Date.now() - startTime

    // Log slow requests
    if (responseTime > 1000) {
      console.warn(`Slow request: ${request.method()} ${request.url()} - ${responseTime}ms`)
    }

    // Add performance header
    response.header('X-Response-Time', `${responseTime}ms`)
  }
}
```

## Usage Examples

### Single Day Request (Cache Miss)

```typescript
// First request - 3 queries (relationship context + activities + availability)
const response = await fetch('/api/v1/availability/user-uuid/2024-03-20')
const data = await response.json()

console.log(data.performance)
// Output: { responseTimeMs: 45, cacheHit: false, estimatedQueries: 3 }
```

### Single Day Request (Cache Hit)

```typescript
// Subsequent request within 15 minutes - 2 queries (activities + availability)
const response = await fetch('/api/v1/availability/user-uuid/2024-03-21')
const data = await response.json()

console.log(data.performance)
// Output: { responseTimeMs: 25, cacheHit: true, estimatedQueries: 2 }
```

### Date Range Request

```typescript
// Week view - relationship context cached after first day
const response = await fetch('/api/v1/availability/user-uuid/2024-03-20/2024-03-26')
const data = await response.json()

console.log(data.performance)
// Output: {
//   responseTimeMs: 120,
//   totalDays: 7,
//   totalEstimatedQueries: 15, // 3 + (6 * 2)
//   cacheHitRate: "6/7",
//   avgQueriesPerDay: 2.14
// }
```

## Summary

This optimized implementation achieves the target of **2-3 queries per request** while maintaining all the sophisticated privacy controls of the original system. Key benefits:

1. **No Database Schema Changes**: Uses existing table structure with strategic indexing
2. **Intelligent Caching**: Redis caching with event-driven invalidation
3. **Optimized SQL**: Single CTE query for relationship context, strategic eager loading
4. **In-Memory Processing**: Policy rule evaluation without additional queries
5. **Performance Monitoring**: Built-in metrics for optimization tracking
6. **Scalable**: Caching reduces load as user base grows

The implementation preserves all original functionality while dramatically improving performance through smart caching and query optimization.
