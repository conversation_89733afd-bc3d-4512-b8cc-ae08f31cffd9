# SkedAI Policy Logic Implementation

This document provides comprehensive implementation details for the SkedAI availability and privacy policy system. It includes the complete code implementation with detailed comments explaining the database design, policy evaluation logic, and system architecture.

## System Overview

The SkedAI policy system enables fine-grained control over activity visibility and scheduling permissions through a sophisticated template-based approach. Users can create reusable policy templates that define who can see their activities and whether they can book time during those activities.

### Key Components

1. **Policy Templates**: Reusable privacy configurations (`privacy_policy_template`)
2. **Template Rules**: Specific rules within templates for different viewer types (`policy_template_rule`)
3. **Policy Assignments**: Links activities to policy templates (`privacy_policy_assignment`)
4. **Policy Categories**: Organizational categories for templates (`policy_category`)

### Database Design Context

The system uses a supertype/subtype pattern where:

- `activity` is the parent table containing common fields for all activity types
- `task`, `appointment`, and `event` are subtypes with specific fields
- Policy assignments reference the `activity.id` directly, allowing unified policy application

## Complete Implementation

### Core Service Class

```typescript
// app/services/AvailabilityService.ts
import { DateTime } from 'luxon'
import Activity from '#models/Activity'
import UserAvailabilitySlot from '#models/UserAvailabilitySlot'
import PolicyTemplateRule from '#models/PolicyTemplateRule'
import UserContact from '#models/UserContact'
import GroupMember from '#models/GroupMember'
import ContactListMember from '#models/ContactListMember'

/**
 * AvailabilityService handles the complex logic of determining what availability
 * information a requester can see based on the target user's privacy policies.
 *
 * The service implements the policy template system which allows users to:
 * 1. Define reusable privacy templates with specific rules
 * 2. Apply different visibility levels based on viewer relationship
 * 3. Control whether viewers can schedule time during activities
 * 4. Override template behavior on a per-activity basis
 */
export default class AvailabilityService {
  /**
   * Main method to get availability for a target user on a specific day.
   * This method orchestrates the entire policy evaluation process:
   *
   * 1. Retrieves general availability slots (recurring and specific)
   * 2. Fetches activities with their policy assignments and rules
   * 3. Determines the requester's relationship context to the target
   * 4. Applies policy rules to filter and transform activity data
   * 5. Returns appropriately filtered availability information
   *
   * @param requesterProfileId - The profile ID of the user requesting availability
   * @param targetProfileId - The profile ID of the user whose availability is being requested
   * @param date - The specific date for which availability is requested
   * @returns Filtered availability data based on policy rules
   */
  async getAvailabilityForDay(requesterProfileId: string, targetProfileId: string, date: DateTime) {
    // Step 1: Get general availability slots for the target user
    // This includes both recurring slots (e.g., "Mon-Fri 9am-5pm") and
    // specific one-time slots (e.g., "Dec 25 unavailable")
    const availabilitySlots = await UserAvailabilitySlot.query()
      .where('profileId', targetProfileId)
      .where((query) => {
        // Query for recurring slots that match this day of the week
        query.where('dayOfWeek', date.weekday).whereNull('specificStartDate') // Recurring slots have null specific dates
        // OR query for specific slots that match this exact date
        query.orWhere((subQuery) => {
          subQuery
            .whereRaw('DATE(specific_start_date) = ?', [date.toISODate()])
            .whereNotNull('specificStartDate') // Specific slots have actual dates
        })
      })
      .exec()

    // Step 2: Get all activities for the target user on the requested day
    // We use eager loading to fetch policy assignments, templates, and rules
    // in a single database round trip for optimal performance
    const activities = await Activity.query()
      .where('profileId', targetProfileId)
      .whereRaw('DATE(start_date) = ?', [date.toISODate()])
      .preload('policyAssignment', (assignmentQuery) => {
        // Load the policy template assigned to this activity
        assignmentQuery.preload('template', (templateQuery) => {
          // Load all rules within the template for policy evaluation
          templateQuery.preload('rules')
        })
      })
      .exec()

    // Step 3: Determine the requester's relationship context to the target user
    // This context is crucial for policy rule evaluation
    const requesterContext = await this.getRequesterContext(requesterProfileId, targetProfileId)

    // Step 4: Filter and transform activities based on policy rules
    // Each activity is evaluated against its policy template rules
    const visibleActivities = []

    for (const activity of activities) {
      const visibilityResult = await this.evaluateActivityVisibility(activity, requesterContext)

      // Only include activities that are visible to the requester
      if (visibilityResult.isVisible) {
        visibleActivities.push({
          id: activity.id,
          startDate: activity.startDate,
          endDate: activity.endDate,
          title: this.getVisibleTitle(activity, visibilityResult.detailLevel),
          description: this.getVisibleDescription(activity, visibilityResult.detailLevel),
          location: this.getVisibleLocation(activity, visibilityResult.detailLevel),
          blocksScheduling: visibilityResult.blocksScheduling,
          customMessage: visibilityResult.customMessage,
          detailLevel: visibilityResult.detailLevel,
        })
      }
    }

    return {
      date: date.toISODate(),
      targetProfileId,
      generalAvailability: availabilitySlots,
      activities: visibleActivities,
      requesterContext, // Useful for debugging and UI adaptation
    }
  }

  /**
   * Determines the requester's relationship context to the target user.
   * This context is used to evaluate which policy template rules apply.
   *
   * The relationship context includes:
   * - Whether they are contacts (accepted contact relationship)
   * - Shared groups they both belong to
   * - Shared contact lists they both appear in
   *
   * This information is essential for policy rule matching because rules
   * can target specific relationships (e.g., "show full details to work contacts")
   *
   * @param requesterProfileId - The requester's profile ID
   * @param targetProfileId - The target user's profile ID
   * @returns Object containing relationship context information
   */
  private async getRequesterContext(requesterProfileId: string, targetProfileId: string) {
    // Check if they have an accepted contact relationship (bidirectional)
    // Contact relationships can be initiated by either party
    const contactRelation = await UserContact.query()
      .where((query) => {
        // Case 1: Requester initiated the contact request
        query
          .where('requesterProfileId', requesterProfileId)
          .where('addresseeProfileId', targetProfileId)
          .where('status', 'accepted')
      })
      .orWhere((query) => {
        // Case 2: Target initiated the contact request
        query
          .where('requesterProfileId', targetProfileId)
          .where('addresseeProfileId', requesterProfileId)
          .where('status', 'accepted')
      })
      .first()

    // Find all groups that both users are members of
    // This is used for "specific_group" policy rules
    const sharedGroups = await GroupMember.query()
      .select('groupId')
      .where('profileId', requesterProfileId)
      .whereIn('groupId', (subQuery) => {
        subQuery.select('groupId').from('group_members').where('profileId', targetProfileId)
      })
      .exec()

    // Find all contact lists that include both users
    // This is used for "specific_contact_list" policy rules
    const sharedContactLists = await ContactListMember.query()
      .select('listId')
      .where('profileId', requesterProfileId)
      .whereIn('listId', (subQuery) => {
        subQuery.select('listId').from('contact_list_members').where('profileId', targetProfileId)
      })
      .exec()

    return {
      isContact: !!contactRelation,
      sharedGroupIds: sharedGroups.map((g) => g.groupId),
      sharedContactListIds: sharedContactLists.map((cl) => cl.listId),
      requesterProfileId, // Store for specific_contact rule matching
    }
  }

  /**
   * Evaluates what the requester can see for a specific activity based on
   * the activity's policy template and the requester's relationship context.
   *
   * Policy Evaluation Process:
   * 1. Check if activity has a policy template assigned
   * 2. If no template, apply default behavior (show as "Busy", block scheduling)
   * 3. If template exists, evaluate rules in priority order
   * 4. Find the first rule that matches the requester's context
   * 5. Apply that rule's visibility settings
   * 6. If no rule matches, use template's default settings
   *
   * Override Behavior:
   * The privacy_policy_assignment table can override specific template settings
   * for individual activities, providing fine-grained control.
   *
   * @param activity - The activity object with loaded policy assignment
   * @param requesterContext - The requester's relationship context
   * @returns Visibility evaluation result
   */
  private async evaluateActivityVisibility(activity: any, requesterContext: any) {
    // If no policy assignment exists, use system default behavior
    // Default: visible as "Busy", blocks scheduling, no custom message
    if (!activity.policyAssignment || !activity.policyAssignment.template) {
      return {
        isVisible: true,
        detailLevel: 'busy_only',
        blocksScheduling: true,
        customMessage: null,
      }
    }

    const template = activity.policyAssignment.template
    const assignment = activity.policyAssignment
    const rules = template.rules || []

    // Sort rules by priority (highest priority first)
    // Higher priority rules override lower priority ones
    const sortedRules = rules.sort((a, b) => b.priority - a.priority)

    // Find the first rule that applies to the requester
    for (const rule of sortedRules) {
      if (this.doesRuleApplyToRequester(rule, requesterContext)) {
        return {
          isVisible: rule.detailVisibility !== 'hidden',
          detailLevel: rule.detailVisibility,
          blocksScheduling: rule.blocksScheduling,
          customMessage: rule.customMessage,
        }
      }
    }

    // No specific rule matched, use template defaults
    // Check for assignment-level overrides first
    const detailLevel = assignment.overrideDetailVisibility || template.defaultDetailVisibility
    const blocksScheduling = assignment.overrideBlocksScheduling ?? template.blocksScheduling
    const customMessage = assignment.overrideCustomMessage || template.defaultCustomMessage

    return {
      isVisible: detailLevel !== 'hidden',
      detailLevel,
      blocksScheduling,
      customMessage,
    }
  }

  /**
   * Determines whether a specific policy rule applies to the requester
   * based on their relationship context to the target user.
   *
   * Rule Scope Types:
   * - public: Always applies to everyone
   * - all_contacts: Applies to any accepted contact
   * - specific_contact: Applies to a specific profile ID
   * - specific_group: Applies to members of a specific group
   * - specific_contact_list: Applies to members of a specific contact list
   *
   * @param rule - The policy template rule to evaluate
   * @param requesterContext - The requester's relationship context
   * @returns Boolean indicating if the rule applies
   */
  private doesRuleApplyToRequester(rule: any, requesterContext: any): boolean {
    switch (rule.viewerScopeType) {
      case 'public':
        // Public rules apply to everyone, including non-contacts
        return true

      case 'all_contacts':
        // Rule applies to any user with an accepted contact relationship
        return requesterContext.isContact

      case 'specific_contact':
        // Rule applies to a specific contact (identified by user ID)
        return (
          requesterContext.isContact && rule.viewerTargetUserId === requesterContext.requesterUserId
        )

      case 'specific_group':
        // Rule applies to members of a specific group
        return requesterContext.sharedGroupIds.includes(rule.viewerTargetGroupId)

      case 'specific_contact_list':
        // Rule applies to members of a specific contact list
        return requesterContext.sharedContactListIds.includes(rule.viewerTargetContactListId)

      default:
        // Unknown scope type, default to not applying
        return false
    }
  }

  /**
   * Transforms the activity title based on the visibility detail level.
   * This implements the privacy filtering at the data presentation level.
   *
   * Detail Levels:
   * - hidden: Activity is completely hidden (returns null)
   * - busy_only: Shows generic "Busy" message
   * - title_only: Shows the actual activity title
   * - full_details: Shows the actual activity title
   *
   * @param activity - The activity object
   * @param detailLevel - The visibility detail level from policy evaluation
   * @returns The visible title or null
   */
  private getVisibleTitle(activity: any, detailLevel: string): string | null {
    switch (detailLevel) {
      case 'hidden':
        return null
      case 'busy_only':
        return 'Busy'
      case 'title_only':
      case 'full_details':
        return activity.title
      default:
        return 'Busy'
    }
  }

  /**
   * Transforms the activity description based on the visibility detail level.
   * Only full_details level shows the actual description.
   *
   * @param activity - The activity object
   * @param detailLevel - The visibility detail level from policy evaluation
   * @returns The visible description or null
   */
  private getVisibleDescription(activity: any, detailLevel: string): string | null {
    return detailLevel === 'full_details' ? activity.description : null
  }

  /**
   * Transforms the activity location based on the visibility detail level.
   * Only full_details level shows the actual location information.
   *
   * @param activity - The activity object
   * @param detailLevel - The visibility detail level from policy evaluation
   * @returns The visible location or null
   */
  private getVisibleLocation(activity: any, detailLevel: string): any | null {
    return detailLevel === 'full_details' ? activity.location : null
  }
}
```

### Controller Implementation

```typescript
// app/controllers/AvailabilityController.ts
import { HttpContext } from '@adonisjs/core/http'
import { DateTime } from 'luxon'
import AvailabilityService from '#services/AvailabilityService'

/**
 * AvailabilityController handles HTTP requests for availability information.
 * It serves as the API layer for the availability and policy system.
 */
export default class AvailabilityController {
  /**
   * GET /availability/:profileId/:date
   *
   * Retrieves availability information for a target user on a specific date,
   * filtered according to the requesting user's permissions based on policy templates.
   *
   * Route Parameters:
   * - profileId: The UUID of the target user's profile
   * - date: ISO date string (YYYY-MM-DD) for the requested day
   *
   * Authentication: Required (requester's profile ID extracted from auth)
   *
   * Response Structure:
   * {
   *   date: "2024-03-20",
   *   targetProfileId: "uuid",
   *   generalAvailability: [...], // General availability slots
   *   activities: [...], // Filtered activities with appropriate detail levels
   *   requesterContext: {...} // Relationship context for debugging
   * }
   */
  async getAvailability({ params, auth, response }: HttpContext) {
    try {
      // Extract authenticated user's profile ID
      // This assumes the auth system populates the user object with profile relationship
      const requesterProfileId = auth.user!.profile.id

      // Extract target profile ID from route parameters
      const targetProfileId = params.profileId

      // Parse and validate the requested date
      const date = DateTime.fromISO(params.date)
      if (!date.isValid) {
        return response.badRequest({
          message: 'Invalid date format. Use YYYY-MM-DD format.',
        })
      }

      // Initialize the availability service and process the request
      const availabilityService = new AvailabilityService()
      const availability = await availabilityService.getAvailabilityForDay(
        requesterProfileId,
        targetProfileId,
        date
      )

      return response.ok(availability)
    } catch (error) {
      // Log error for debugging while providing user-friendly response
      console.error('Availability fetch error:', error)
      return response.badRequest({
        message: 'Failed to fetch availability information',
      })
    }
  }

  /**
   * GET /availability/:profileId/:startDate/:endDate
   *
   * Retrieves availability information for a date range.
   * This method can be used for calendar views showing multiple days.
   *
   * @param HttpContext containing params, auth, and response
   */
  async getAvailabilityRange({ params, auth, response }: HttpContext) {
    try {
      const requesterProfileId = auth.user!.profile.id
      const targetProfileId = params.profileId

      const startDate = DateTime.fromISO(params.startDate)
      const endDate = DateTime.fromISO(params.endDate)

      if (!startDate.isValid || !endDate.isValid) {
        return response.badRequest({
          message: 'Invalid date format. Use YYYY-MM-DD format.',
        })
      }

      if (endDate < startDate) {
        return response.badRequest({
          message: 'End date must be after start date.',
        })
      }

      // Limit range to prevent excessive queries (e.g., max 31 days)
      const daysDiff = endDate.diff(startDate, 'days').days
      if (daysDiff > 31) {
        return response.badRequest({
          message: 'Date range cannot exceed 31 days.',
        })
      }

      const availabilityService = new AvailabilityService()
      const results = []

      // Process each day in the range
      let currentDate = startDate
      while (currentDate <= endDate) {
        const dayAvailability = await availabilityService.getAvailabilityForDay(
          requesterProfileId,
          targetProfileId,
          currentDate
        )
        results.push(dayAvailability)
        currentDate = currentDate.plus({ days: 1 })
      }

      return response.ok({
        startDate: startDate.toISODate(),
        endDate: endDate.toISODate(),
        targetProfileId,
        days: results,
      })
    } catch (error) {
      console.error('Availability range fetch error:', error)
      return response.badRequest({
        message: 'Failed to fetch availability range',
      })
    }
  }
}
```

### Route Definition

```typescript
// start/routes.ts
import router from '@adonisjs/core/services/router'
import { middleware } from './kernel.js'

// Availability routes - require authentication
router
  .group(() => {
    // Get availability for a specific day
    router.get('/availability/:profileId/:date', [AvailabilityController, 'getAvailability'])

    // Get availability for a date range
    router.get('/availability/:profileId/:startDate/:endDate', [
      AvailabilityController,
      'getAvailabilityRange',
    ])
  })
  .prefix('/api/v1')
  .middleware(middleware.auth())
```

## Usage Examples

### Example 1: Basic Availability Request

```typescript
// Frontend making API request
const response = await fetch('/api/v1/availability/user-profile-uuid/2024-03-20', {
  headers: { Authorization: 'Bearer ' + authToken },
})

const availability = await response.json()
console.log(availability)
// Output:
// {
//   date: "2024-03-20",
//   targetProfileId: "user-profile-uuid",
//   generalAvailability: [...],
//   activities: [
//     {
//       id: "activity-uuid",
//       startDate: "2024-03-20T14:00:00",
//       endDate: "2024-03-20T15:00:00",
//       title: "Client Meeting", // or "Busy" depending on policy
//       description: null, // or actual description for full_details
//       location: null, // or actual location for full_details
//       blocksScheduling: true,
//       customMessage: null,
//       detailLevel: "title_only"
//     }
//   ],
//   requesterContext: {
//     isContact: true,
//     sharedGroupIds: ["work-group-uuid"],
//     sharedContactListIds: []
//   }
// }
```

### Example 2: Policy Template Creation

```typescript
// Service method to create a policy template
async createPolicyTemplate(profileId: string, templateData: any) {
  const template = await PrivacyPolicyTemplate.create({
    profileId,
    name: templateData.name,
    description: templateData.description,
    categoryId: templateData.categoryId,
    blocksScheduling: templateData.blocksScheduling,
    defaultDetailVisibility: templateData.defaultDetailVisibility
  })

  // Create rules for the template
  for (const ruleData of templateData.rules) {
    await PolicyTemplateRule.create({
      templateId: template.id,
      viewerScopeType: ruleData.viewerScopeType,
      viewerTargetGroupId: ruleData.viewerTargetGroupId,
      blocksScheduling: ruleData.blocksScheduling,
      detailVisibility: ruleData.detailVisibility,
      priority: ruleData.priority
    })
  }

  return template
}
```

## Performance Considerations

1. **Eager Loading**: Use `preload()` to fetch related data in single queries
2. **Caching**: Consider caching policy templates and relationship contexts
3. **Indexing**: Ensure proper database indexes on foreign keys and date fields
4. **Batch Processing**: For date ranges, consider batch loading instead of individual day queries

## Security Considerations

1. **Authentication**: Always verify the requester's identity
2. **Authorization**: Ensure requesters can only access permitted profile data
3. **Rate Limiting**: Implement rate limiting to prevent abuse
4. **Input Validation**: Validate all input parameters, especially dates and UUIDs

## Future AI Agent Guidance

When working with this system:

1. **Understanding Policy Rules**: Rules are evaluated in priority order, with higher numbers taking precedence
2. **Context Relationships**: Always determine the requester's relationship context before applying rules
3. **Default Behavior**: When no policy is assigned, default to showing "Busy" with blocked scheduling
4. **Override Capabilities**: Remember that individual activities can override template settings
5. **Performance**: Use eager loading and consider caching for frequently accessed data
6. **Testing**: Test with various relationship contexts (contacts, groups, strangers) to ensure proper privacy enforcement

This implementation provides a robust, scalable foundation for complex availability and privacy management in the SkedAI system.

#### Policy Template System Tables

This system allows users to define reusable privacy and availability policies that can be quickly applied to activities.

##### `policy_category`

- **Purpose**: Defines categories or themes for policy templates (e.g., "Work Hours", "Personal Time", "Emergency").
- **Key Features**: `key`, `label`, `description`, `suggestedKeywords` (JSONB for smart suggestions), `isSystemContext` (for predefined categories), `profileId` (for user-defined categories).

###### Role of Policy Category

The `policy_category` entity plays a specific role from technical, user, and design perspectives:

- **Technical Role**:

  - **Data Structure**: Stores metadata for categories (label, description, keywords, system/user ownership).
  - **Relationship**: Linked to `privacy_policy_template` via `categoryId`.
  - **Runtime Logic**: Does _not_ directly influence runtime policy evaluation.
  - **Purpose**: Provides structured data for organizing and suggesting policy templates, supporting UI and potential AI features.

- **User-wise Role**:

  - **Organization**: Helps users categorize policy templates (e.g., "Work Hours", "Personal Time").
  - **Clarity & Intuition**: Provides clear thematic labels, making template purpose understandable.
  - **Discovery & Selection**: Simplifies finding the right template by allowing users to filter or search by category.
  - **Potential AI Assistance**: Keywords aid AI in suggesting relevant categories/templates based on activity descriptions.

- **Design-wise Role**:
  - **UI Element**: Requires a dropdown or selection control for category in template forms.
  - **Information Display**: Provides `label` and `description` for UI elements.
  - **Structure**: Suggests grouping templates by category in UI lists.
  - **Navigation/Views**: Justifies a "Manage Categories" area for user-defined categories.
  - **Visual Cues**: `isSystemContext` allows visual differentiation in the UI.

##### `privacy_policy_template`

- **Purpose**: Stores reusable policy templates created by users or provided by the system.
- **Key Features**: `name`, `description`, `categoryId` (links to `policy_category`), `isDefault`, `isSystemTemplate`, `blocksScheduling`, and `defaultDetailVisibility`.

###### Table-Specific Examples

To illustrate the role of each table:

- **`user_availability_slot`**: User sets recurring availability: "Available Mon-Fri 9am-5pm". User adds a one-time slot for a vacation week: "Unavailable July 1-5 all day". _Edge Case_: User marks a specific future hour as `blocksScheduling: false` to intentionally show "Busy" without linking it to an activity.
- **`policy_category`**: System creates a "Work Hours" category (`isSystemContext: true`). User creates a "Hobby Time" category (`profileId: user-id`). The "Work Hours" category has `suggestedKeywords: ["meeting", "call"]`.
- **`privacy_policy_template`**: User creates a template named "Gym Time" and links it to the "Personal Time" `policy_category`. User marks their "Default Work" template as `isDefault: true`. The "Gym Time" template has `blocksScheduling: true` and `defaultDetailVisibility: busy_only`.

**Step 1: Define a Category (if not already system-defined)**
If a "Client Meeting" category doesn't exist (either system-wide or user-defined), it would be added to `policy_category`.

```sql
-- Example row in `policy_category` table
{
  "id": "client-category-uuid",
  "profileId": null, -- System category
  "key": "client_meeting",
  "label": "Client Meeting",
  "description": "Policies for meetings with clients",
  "suggestedKeywords": ["client", "meeting", "call", "demo"],
  "isSystemContext": true,
  "isActive": true,
  "displayOrder": 10
}
```

**Step 2: Create the Policy Template**
Sarah creates a new template for client meetings, linking it to the "Client Meeting" category.

```sql
-- Example row in `privacy_policy_template` table
{
  "id": "client-template-uuid",
  "profileId": "sarah-profile-uuid",
  "name": "Client Meeting Privacy",
  "description": "Rules for external client interactions",
  "categoryId": "client-category-uuid",
  "isDefault": false,
  "isSystemTemplate": false,
  "blocksScheduling": true, -- Default blocks scheduling
  "defaultDetailVisibility": "busy_only", -- Default is busy only
  "createdAt": "...",
  "updatedAt": "...",
  "deletedAt": null
}
```
