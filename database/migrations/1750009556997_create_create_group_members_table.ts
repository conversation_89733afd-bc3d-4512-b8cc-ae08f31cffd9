import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'group_member'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      table
        .uuid('group_id')
        .notNullable()
        .references('id')
        .inTable('user_group')
        .onDelete('CASCADE')

      table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE')

      table.enum('role', ['owner', 'admin', 'member']).notNullable().defaultTo('member')

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())
      table.timestamp('deleted_at').nullable()

      // Indexes for performance
      table.index(['group_id'], 'idx_group_member_group_id')
      table.index(['user_id'], 'idx_group_member_user_id')
      table.index(['role'], 'idx_group_member_role')
      table.index(['deleted_at'], 'idx_group_member_deleted_at')

      // Unique constraint to prevent duplicate memberships
      table.unique(['group_id', 'user_id'], 'unique_group_member_pair')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
