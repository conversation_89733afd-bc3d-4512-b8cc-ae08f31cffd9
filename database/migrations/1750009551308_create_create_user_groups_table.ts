import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'user_group'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      table
        .uuid('owner_user_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')

      table.string('name', 255).notNullable()
      table.text('description').nullable()

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())
      table.timestamp('deleted_at').nullable()

      // Indexes for performance
      table.index(['owner_user_id'], 'idx_user_group_owner_user_id')
      table.index(['name'], 'idx_user_group_name')
      table.index(['deleted_at'], 'idx_user_group_deleted_at')
      table.index(['created_at'], 'idx_user_group_created_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
