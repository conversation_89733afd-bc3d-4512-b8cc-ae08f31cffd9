import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'group_invitation_request'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      table
        .uuid('group_id')
        .notNullable()
        .references('id')
        .inTable('user_group')
        .onDelete('CASCADE')

      table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE')

      table.enum('type', ['invitation', 'request']).notNullable()

      table.enum('status', ['pending', 'accepted', 'declined']).notNullable().defaultTo('pending')

      table
        .uuid('initiator_user_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')

      table.text('message').nullable()

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())
      table.timestamp('deleted_at').nullable()

      // Indexes for performance
      table.index(['group_id'], 'idx_group_invitation_request_group_id')
      table.index(['user_id'], 'idx_group_invitation_request_user_id')
      table.index(['initiator_user_id'], 'idx_group_invitation_request_initiator_user_id')
      table.index(['type'], 'idx_group_invitation_request_type')
      table.index(['status'], 'idx_group_invitation_request_status')
      table.index(['deleted_at'], 'idx_group_invitation_request_deleted_at')
      table.index(['created_at'], 'idx_group_invitation_request_created_at')

      // Unique constraint to prevent duplicate invitations/requests
      table.unique(['group_id', 'user_id', 'type'], 'unique_group_invitation_request')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
