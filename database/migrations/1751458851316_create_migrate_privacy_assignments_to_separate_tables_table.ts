import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  async up() {
    // Create single privacy_assignments table with nullable foreign keys
    this.schema.createTable('privacy_assignments', (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      // Nullable foreign keys - exactly one must be set
      table.uuid('task_id').nullable().references('id').inTable('tasks').onDelete('CASCADE')

      table.uuid('event_id').nullable().references('id').inTable('events').onDelete('CASCADE')

      table
        .uuid('template_id')
        .notNullable()
        .references('id')
        .inTable('privacy_policy_templates')
        .onDelete('CASCADE')

      // Override fields (optional, inherits from template if null)
      table.boolean('override_blocks_scheduling').nullable()
      table
        .enum('override_detail_visibility', ['hidden', 'busy_only', 'title_only', 'full_details'])
        .nullable()
      table.text('override_custom_message').nullable()

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())
    })

    // Add constraints and indexes
    this.schema.alterTable('privacy_assignments', (table) => {
      // Indexes for performance
      table.index('task_id', 'idx_privacy_assignments_task_id')
      table.index('event_id', 'idx_privacy_assignments_event_id')
      table.index('template_id', 'idx_privacy_assignments_template_id')
    })

    // Add CHECK constraint using raw SQL
    this.schema.raw(`
      ALTER TABLE privacy_assignments 
      ADD CONSTRAINT check_exactly_one_resource 
      CHECK ((task_id IS NOT NULL AND event_id IS NULL) OR (task_id IS NULL AND event_id IS NOT NULL))
    `)

    // Add partial unique constraints
    this.schema.raw(`
      CREATE UNIQUE INDEX idx_privacy_assignments_task_template_unique 
      ON privacy_assignments(task_id, template_id) 
      WHERE task_id IS NOT NULL
    `)

    this.schema.raw(`
      CREATE UNIQUE INDEX idx_privacy_assignments_event_template_unique 
      ON privacy_assignments(event_id, template_id) 
      WHERE event_id IS NOT NULL
    `)

    // Migrate existing data from polymorphic table
    const existingAssignments = await this.db.from('privacy_policy_assignments').select('*')

    for (const assignment of existingAssignments) {
      const insertData = {
        id: assignment.id,
        task_id: assignment.activity_type === 'task' ? assignment.activity_id : null,
        event_id: assignment.activity_type === 'event' ? assignment.activity_id : null,
        template_id: assignment.template_id,
        override_blocks_scheduling: assignment.override_blocks_scheduling,
        override_detail_visibility: assignment.override_detail_visibility,
        override_custom_message: assignment.override_custom_message,
        created_at: assignment.created_at,
        updated_at: assignment.updated_at,
      }

      await this.db.table('privacy_assignments').insert(insertData)
    }

    // Drop the old polymorphic table
    this.schema.dropTable('privacy_policy_assignments')
  }

  async down() {
    // Recreate the polymorphic table
    this.schema.createTable('privacy_policy_assignments', (table) => {
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      // Polymorphic relationship to tasks or events
      table.uuid('activity_id').notNullable()
      table.enum('activity_type', ['task', 'event']).notNullable()

      table
        .uuid('template_id')
        .notNullable()
        .references('id')
        .inTable('privacy_policy_templates')
        .onDelete('CASCADE')

      // Override fields (optional, inherits from template if null)
      table.boolean('override_blocks_scheduling').nullable()
      table
        .enum('override_detail_visibility', ['hidden', 'busy_only', 'title_only', 'full_details'])
        .nullable()
      table.text('override_custom_message').nullable()

      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())

      // Unique constraint to prevent duplicate assignments
      table.unique(['activity_id', 'activity_type', 'template_id'])
    })

    // Add indexes for performance
    this.schema.alterTable('privacy_policy_assignments', (table) => {
      table.index(['activity_id', 'activity_type'])
      table.index('template_id')
    })

    // Migrate data back from single table with nullable FKs
    const assignments = await this.db.from('privacy_assignments').select('*')

    for (const assignment of assignments) {
      const insertData = {
        id: assignment.id,
        activity_id: assignment.task_id || assignment.event_id,
        activity_type: assignment.task_id ? 'task' : 'event',
        template_id: assignment.template_id,
        override_blocks_scheduling: assignment.override_blocks_scheduling,
        override_detail_visibility: assignment.override_detail_visibility,
        override_custom_message: assignment.override_custom_message,
        created_at: assignment.created_at,
        updated_at: assignment.updated_at,
      }

      await this.db.table('privacy_policy_assignments').insert(insertData)
    }

    // Drop the single table
    this.schema.dropTable('privacy_assignments')
  }
}
