import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'commitments'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().defaultTo(this.raw('gen_random_uuid()'))

      // Resource References (exactly one must be set)
      table.uuid('task_id').nullable().references('id').inTable('tasks').onDelete('CASCADE')
      table.uuid('event_id').nullable().references('id').inTable('events').onDelete('CASCADE')

      // User Relationships
      table.uuid('host_user_id').notNullable().references('id').inTable('users').onDelete('CASCADE')
      table
        .uuid('invitee_user_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')

      // Status Tracking
      table.enum('invitee_status', ['pending', 'accepted', 'declined']).defaultTo('pending')
      table.enum('host_status', ['pending', 'accepted', 'declined']).defaultTo('accepted')

      // Optional Data
      table.text('message').nullable()
      table.boolean('is_auto_accepted').defaultTo(false)

      // Timestamps
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })

    // Add CHECK constraint after table creation
    this.schema.raw(`
      ALTER TABLE commitments 
      ADD CONSTRAINT check_exactly_one_resource 
      CHECK ((task_id IS NOT NULL AND event_id IS NULL) OR (task_id IS NULL AND event_id IS NOT NULL))
    `)

    // Create partial unique indexes to prevent duplicate invitations
    this.schema.raw(`
      CREATE UNIQUE INDEX idx_commitments_task_unique 
      ON commitments(task_id, host_user_id, invitee_user_id) 
      WHERE task_id IS NOT NULL AND deleted_at IS NULL
    `)

    this.schema.raw(`
      CREATE UNIQUE INDEX idx_commitments_event_unique 
      ON commitments(event_id, host_user_id, invitee_user_id) 
      WHERE event_id IS NOT NULL AND deleted_at IS NULL
    `)

    // Performance indexes
    this.schema.raw(`
      CREATE INDEX idx_commitments_task_id ON commitments(task_id) WHERE task_id IS NOT NULL
    `)

    this.schema.raw(`
      CREATE INDEX idx_commitments_event_id ON commitments(event_id) WHERE event_id IS NOT NULL
    `)

    this.schema.raw(`
      CREATE INDEX idx_commitments_host_user ON commitments(host_user_id) WHERE deleted_at IS NULL
    `)

    this.schema.raw(`
      CREATE INDEX idx_commitments_invitee_user ON commitments(invitee_user_id) WHERE deleted_at IS NULL
    `)

    this.schema.raw(`
      CREATE INDEX idx_commitments_invitee_status ON commitments(invitee_status) WHERE deleted_at IS NULL
    `)
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
