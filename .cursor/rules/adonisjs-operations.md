# AdonisJS Operations Guidelines

## Routing

- Group related routes together
- Use route resources for RESTful controllers
- Add descriptive names to routes
- Prefix API routes with version numbers
- Apply middleware at the route group level when possible
- Use route model binding for fetching models
- Leverage route closures for simple responses only
- Use lazy-loaded controllers for better performance

```typescript
import router from '@adonisjs/core/services/router'

const TasksController = () => import('#controllers/tasks_controller')

router
  .group(() => {
    router.get('tasks', [TasksController, 'index']).as('tasks.index')
    router.post('tasks', [TasksController, 'store']).as('tasks.store')
    router.get('tasks/:id', [TasksController, 'show']).as('tasks.show')
    router.put('tasks/:id', [TasksController, 'update']).as('tasks.update')
    router.delete('tasks/:id', [TasksController, 'destroy']).as('tasks.destroy')
  })
  .prefix('/api/v1')
  .middleware(['auth'])
```

## Validation

- Use VineJS for request validation
- Create dedicated validator classes for complex validation rules
- Reuse validation schemas across validators
- Use custom rules when built-in rules aren't sufficient
- Add proper error messages
- Use validation middleware for routes
- Sanitize inputs in addition to validation

```typescript
import vine from '@vinejs/vine'
import { HttpContext } from '@adonisjs/core/http'

export default class TaskValidator {
  public async validateCreate({ request, response }: HttpContext) {
    const schema = vine.object({
      title: vine.string().trim().minLength(3).maxLength(255),
      description: vine.string().trim().nullable(),
      due_date: vine.date().nullable(),
      status: vine.enum(['pending', 'in_progress', 'completed']).optional(),
    })

    try {
      return await vine.validate({ schema, data: request.body() })
    } catch (error) {
      return response.badRequest({ errors: error.messages })
    }
  }
}
```

## Error Handling

- Create custom exception classes for domain errors
- Implement consistent error responses
- Add error codes for easier troubleshooting
- Log errors with appropriate severity
- Return user-friendly error messages
- Use try/catch for expected exceptions
- Use global exception handler for consistent error responses

```typescript
// Custom exception
import { Exception } from '@adonisjs/core/exceptions'

export class TaskNotFoundException extends Exception {
  constructor(id: number) {
    super(`Task with ID ${id} not found`, { status: 404, code: 'E_TASK_NOT_FOUND' })
  }
}

// Global exception handler
import { HttpContext } from '@adonisjs/core/http'
import Logger from '@adonisjs/core/services/logger'

export async function handle({ request, response }: HttpContext, error: any) {
  if (error.code === 'E_TASK_NOT_FOUND') {
    return response.notFound({ error: error.message })
  }

  Logger.error(error)
  return response.internalServerError({ error: 'An unexpected error occurred' })
}
```

## Database Operations

- Use migrations for database schema changes
- Create seeders for test data
- Implement transactions for operations that need to be atomic
- Leverage query builders for complex queries
- Create dedicated repositories for complex database operations
- Use proper indexes on frequently queried fields
- Add database indexes strategically

```typescript
// Using transactions
import db from '@adonisjs/lucid/services/db'

async function transferFunds(fromAccountId, toAccountId, amount) {
  return db.transaction(async (trx) => {
    const fromAccount = await Account.findOrFail(fromAccountId)
    const toAccount = await Account.findOrFail(toAccountId)

    fromAccount.balance -= amount
    toAccount.balance += amount

    await fromAccount.useTransaction(trx).save()
    await toAccount.useTransaction(trx).save()

    return { fromAccount, toAccount }
  })
}

// Using query builders
async function findActiveTasksByUser(userId, page = 1, limit = 20) {
  return Task.query()
    .where('user_id', userId)
    .where('status', 'active')
    .orderBy('due_date', 'asc')
    .paginate(page, limit)
}
```

## Authentication

- Use the built-in authentication providers
- Implement proper password hashing
- Use token-based authentication for APIs
- Implement refresh token rotation for better security
- Apply auth middleware to protected routes
- Create dedicated auth service for complex auth flows
- Implement secure password hashing

```typescript
// Auth middleware on routes
router
  .group(() => {
    router.get('profile', [UserController, 'profile'])
    router.put('profile', [UserController, 'updateProfile'])
  })
  .prefix('/api/v1')
  .middleware(['auth'])

// Auth service
import Hash from '@adonisjs/core/services/hash'
import User from '#models/user'

export default class AuthService {
  async verifyCredentials(email: string, password: string) {
    const user = await User.findBy('email', email)
    if (!user) {
      return null
    }

    const passwordValid = await Hash.verify(user.password, password)
    if (!passwordValid) {
      return null
    }

    return user
  }

  async createToken(user: User) {
    return user.related('tokens').create({
      name: 'api_token',
      type: 'api',
      expiresAt: DateTime.now().plus({ days: 7 }),
    })
  }
}
```

## Performance

- Use eager loading to prevent N+1 queries
- Implement appropriate caching strategies
- Apply pagination for large datasets
- Add database indexes strategically
- Optimize asset loading and bundling
- Measure and profile application performance
- Implement HTTP level optimizations
- Use pagination for large data sets

```typescript
// Eager loading to prevent N+1 queries
async function getUsersWithTasks() {
  return User.query().preload('tasks')
}

// Pagination for large datasets
async function listTasks(page = 1, limit = 20) {
  return Task.query().paginate(page, limit)
}
```

## Security

- Always validate and sanitize user inputs
- Implement CSRF protection for web routes
- Use CORS protection for API routes
- Set proper HTTP security headers
- Implement rate limiting to prevent brute force attacks
- Use environment variables for sensitive information
- Follow the principle of least privilege for database users

```typescript
// CORS configuration
export default {
  enabled: true,
  origin: ['http://localhost:3000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  headers: true,
  credentials: true,
}

// Rate limiting middleware
import { HttpContext } from '@adonisjs/core/http'
import { NextFn } from '@adonisjs/core/types'
import RateLimiter from '@adonisjs/rate-limiter'

export default class RateLimitMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    const key = `rate_limit_${ctx.request.ip()}`
    await RateLimiter.consume(key, 60, 1000) // 60 requests per 1000ms
    await next()
  }
}
```

## Maintenance

- Keep dependencies up to date
- Clean up unused code and dependencies
- Document API changes and breaking changes
- Use semantic versioning for your application
- Implement proper logging for debugging
- Review and optimize database queries regularly
- Monitor application performance in production
