# AdonisJS Best Practices Cursor Rules

This directory contains the cursor rules for AdonisJS projects. The rules are organized into multiple files for better maintainability and easier reference.

## Files Organization

- **clean-adonisjs-typescript-cursor-rules.mdc**: The main cursor rules file that provides a high-level overview of AdonisJS and TypeScript best practices. It references the specialized files for more detailed guidance.

- **typescript-guidelines.md**: Contains detailed TypeScript best practices, including principles, nomenclature, functions, data handling, classes, exceptions, and testing.

- **adonisjs-architecture.md**: Contains detailed guidelines on AdonisJS project structure, controllers, services, models, and middleware.

- **adonisjs-operations.md**: Contains detailed guidelines on routing, validation, error handling, database operations, authentication, performance, and security.

## Usage

When working with Cursor AI, you can refer to specific files for more detailed guidance on particular topics. For example:

- For TypeScript-related questions, refer the AI to `.cursor/rules/typescript-guidelines.md`
- For questions about project structure, controllers, or services, refer to `.cursor/rules/adonisjs-architecture.md`
- For questions about routing, validation, database operations, or security, refer to `.cursor/rules/adonisjs-operations.md`

This organization allows for more focused and relevant guidance from the AI while keeping the content manageable and well-structured.
