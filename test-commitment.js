// Quick test script to debug commitment endpoint
import fetch from 'node-fetch';

async function testCommitmentEndpoint() {
  try {
    // First, let's test if the server is responding
    const healthResponse = await fetch('http://localhost:3333/');
    console.log('Health check status:', healthResponse.status);
    console.log('Health check response:', await healthResponse.text());

    // Test the commitment endpoint without auth (should get 401)
    const commitmentResponse = await fetch('http://localhost:3333/api/v1/tasks/test-id/invitations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        inviteeUserId: 'test-user-id',
        message: 'Test message'
      })
    });

    console.log('Commitment endpoint status:', commitmentResponse.status);
    console.log('Commitment endpoint response:', await commitmentResponse.text());

  } catch (error) {
    console.error('Error testing endpoint:', error);
  }
}

testCommitmentEndpoint();
