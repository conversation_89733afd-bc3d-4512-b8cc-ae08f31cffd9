# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development

```bash
npm run dev          # Start development server with hot reload
npm run build        # Build the application for production
npm start           # Start production server
```

### Code Quality

```bash
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
npm run typecheck    # Run TypeScript type checking
```

### Testing

```bash
npm test            # Run all tests
node ace test       # Alternative test command
```

### Database

```bash
node ace migration:run    # Run database migrations
node ace db:seed         # Seed the database
```

## Architecture

### **Database Architecture: Separate Tables Pattern**

**Key Design (2025-01-25):** The project uses a **separate tables architecture** with direct relationships, NOT polymorphic patterns.

**Database Design:**

- **Separate tables**: `tasks` and `events` with all activity fields included directly
- **Direct relationships**: Related features use separate tables for task and event associations
- **No unified activities table**: Activity model exists but is non-functional (transitional artifact)

**Architecture Pattern:**

```sql
-- Single table with nullable foreign keys:
privacy_assignments (
  task_id NULL → tasks.id,     -- Either task_id is set
  event_id NULL → events.id,   -- Or event_id is set (never both)
  CHECK constraint ensures exactly one is set
)

-- Future tables follow same pattern:
-- commitments (task_id NULL, event_id NULL, CHECK constraint)
-- tags and activity_tags (task_id NULL, event_id NULL)
```

**Implementation Status: ~75% Complete**

- ✅ 22 database tables implemented
- ✅ Advanced privacy system with template-based rules
- ✅ Complete contact and group management
- ✅ AI integration with LangchainJS (Vercel AI SDK migration completed)
- ❌ Privacy system migration to separate tables in progress
- ❌ Collaboration, tagging, availability, and audit features pending

### Feature-Based Structure

The application follows a feature-based architecture under `app/features/`:

- **session/**: Authentication and JWT token management ✅
- **profile/**: User profiles and profile settings ✅
- **contact/**: Contact management including lists and requests ✅
- **group/**: Group management with members and invitations ✅
- **task/**: AI task management with multiple AI service integrations ✅
- **event/**: Event management ✅
- **privacy-policy/**: Privacy policy templates, categories, and rules ✅
- **user/**: User model and related functionality ✅

### Key Patterns

- **Controllers**: Handle HTTP requests, delegate to services
- **Services**: Business logic layer (e.g., `contact_service.ts`, `session_service.ts`)
- **Models**: Database models with relationships and hooks
- **Validators**: Input validation using VineJS
- **Types**: TypeScript type definitions organized by feature

### Import Aliases

The project uses import aliases defined in `package.json`:

- `#controllers/*` → `./app/controllers/*.js`
- `#features/*` → `./app/features/*.js`
- `#models/*` → `./app/models/*.js`
- `#services/*` → `./app/services/*.js`
- `#types/*` → `./app/types/*.js`
- `#utils/*` → `./app/utils/*.js`
- `#validators/*` → `./app/validators/*.js`

### Authentication System

- JWT-based authentication with refresh tokens
- Access tokens expire in 15 minutes, refresh tokens in 7 days
- Auth middleware protects routes under `/api/v1/` (except auth endpoints)
- Session management in `app/features/session/`

### Database

- PostgreSQL with Lucid ORM
- Migrations in `database/migrations/`
- Models use soft deletes via `adonis-lucid-soft-deletes`
- UUID primary keys for most entities

**Current Implementation (23 tables):**

```
✅ Authentication: auth_users, users, profiles, profile_settings, auth_access_tokens, refresh_tokens
✅ Activities: tasks, events (separate tables architecture)
✅ Privacy System: policy_categories, privacy_policy_templates, policy_template_rules, privacy_assignments
✅ Contact System: user_contacts, user_contact_lists, contact_list_members
✅ Group System: user_groups, group_members, group_invitation_requests
```

**Missing Tables (9 remaining):**

```
❌ Collaboration: commitments, activity_log, mentions_to_task
❌ Organization: tag, activity_tag, user_availability_slot
```

### AI Integration

**Migration Status: ✅ COMPLETED** - LangchainJS is primary implementation

- ✅ **LangChain** (`langchain_ai_service.ts`) - ACTIVE implementation with structured outputs
- ⚠️ Vercel AI SDK (`vercel_ai_service.ts`) - Legacy, still present but LangChain is primary
- ✅ Task Master AI (`task-master-ai` package)
- **AI Endpoint**: `POST /api/v1/ai/prompt` for natural language task creation

### Testing

- Unit tests: `tests/unit/` and `app/features/*/tests/unit/`
- Functional tests: `tests/functional/` and `app/features/*/tests/functional/`
- Test configuration in `adonisrc.ts`

### Privacy Policy System

Complex privacy management system with:

- Policy templates and categories
- Rule-based access control
- Assignment system for user privacy settings
- Performance optimization strategies documented in `docs/`

## Development Notes

### Code Style

- Follow AdonisJS best practices from `cursor-rules/adonisjs-best-practices.md`
- Use explicit TypeScript types
- Keep controllers slim, move business logic to services
- Use dependency injection for services
- Implement proper error handling with custom exceptions

### Security

- Rate limiting on API routes
- CORS configuration
- Input validation and sanitization
- Environment-based configuration

## Missing Features & Development Roadmap

### **Priority 1: Collaboration System (2-3 weeks)**

**Critical missing feature for multi-user activities**

**Required Migration:**

```sql
-- Single table with nullable foreign keys and CHECK constraint
CREATE TABLE commitments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NULL REFERENCES tasks(id),
  event_id UUID NULL REFERENCES events(id),
  host_user_id UUID REFERENCES users(id),
  invitee_user_id UUID REFERENCES users(id),
  invitee_status engagement_status_enum DEFAULT 'pending',
  host_status engagement_status_enum DEFAULT 'accepted',
  message TEXT,
  is_auto_accepted BOOLEAN DEFAULT false,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMP,

  -- Ensure exactly one resource is referenced
  CONSTRAINT check_exactly_one_resource CHECK (
    (task_id IS NOT NULL AND event_id IS NULL) OR
    (task_id IS NULL AND event_id IS NOT NULL)
  )
);
```

**Missing API Endpoints:**

```typescript
POST   /api/v1/tasks/:id/invitations     // Send task invitation
POST   /api/v1/events/:id/invitations    // Send event invitation
GET    /api/v1/invitations/received      // Get received invitations
PUT    /api/v1/invitations/:id           // Accept/decline
DELETE /api/v1/invitations/:id          // Cancel
```

### **Priority 2: Organization Tools (1-2 weeks)**

**Tagging and timer functionality**

**Required Migrations:**

```sql
-- Tags table (shared)
CREATE TABLE tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  color VARCHAR(7),
  user_id UUID REFERENCES users(id),
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Single activity_tags table with nullable foreign keys
CREATE TABLE activity_tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NULL REFERENCES tasks(id),
  event_id UUID NULL REFERENCES events(id),
  tag_id UUID REFERENCES tags(id),
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),

  -- Ensure exactly one resource is referenced
  CONSTRAINT check_exactly_one_resource CHECK (
    (task_id IS NOT NULL AND event_id IS NULL) OR
    (task_id IS NULL AND event_id IS NOT NULL)
  ),

  -- Unique constraints for both scenarios
  UNIQUE(task_id, tag_id) WHERE task_id IS NOT NULL,
  UNIQUE(event_id, tag_id) WHERE event_id IS NOT NULL
);
```

**Missing API Endpoints:**

```typescript
POST   /api/v1/tags
POST   /api/v1/tasks/:id/tags        // Add tags to task
POST   /api/v1/events/:id/tags       // Add tags to event
POST   /api/v1/tasks/:id/timer/start
GET    /api/v1/timer/active
```

### **Priority 3: Availability Management (2-3 weeks)**

**User availability slots and scheduling**

**Required Migration:**

```sql
CREATE TABLE user_availability_slots (
  user_id UUID REFERENCES users(id),
  visibility_scope availability_scope_enum,
  -- Complex availability definition fields
);
```

### **Architecture Patterns to Follow**

When implementing missing features:

1. **Single Table with Nullable FKs Pattern**: Use single tables with nullable foreign keys and CHECK constraints
2. **Feature-based Structure**: Place new features under `app/features/[feature]/`
3. **Service Layer**: Business logic in services, controllers only handle HTTP
4. **Comprehensive Testing**: Unit + functional tests for all new features
5. **Soft Deletes**: Include `deletedAt` for all user-facing entities

### **Performance Considerations**

- Add indexes for nullable foreign key relationships with partial indexes: `task_id WHERE task_id IS NOT NULL`, `event_id WHERE event_id IS NOT NULL`
- Consider denormalization for high-frequency availability queries
- Implement caching for privacy policy rule evaluation
