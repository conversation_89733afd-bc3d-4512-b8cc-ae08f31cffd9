import type { HttpContext } from '@adonisjs/core/http'
import { errors as VineErrors } from '@vinejs/vine'
import { authorizationException, notFoundException, parseException } from '#utils/error'
import { createSuccessResponse } from '#utils/response'
import type {
  BaseFilters,
  BaseSort,
  BaseQueryParams,
  PaginatedResponse,
  ResourceWithUser,
} from '#types/base_query_types'

// Base interface for services that work with the controller utilities
export interface BaseResourceService<
  TResource extends ResourceWithUser,
  TFilt<PERSON> extends BaseFilters,
  TSort extends BaseSort,
> {
  getResourceById(id: string): Promise<TResource | null>
  getResources(
    userId: string,
    options: { queryParams?: BaseQueryParams<TFilters, TSort>; paginate?: boolean }
  ): Promise<PaginatedResponse<TResource>>
  deleteResource(id: string, userId: string): Promise<boolean>
  // Optional methods for create and update operations
  createResource?: (userId: string, data: any) => Promise<TResource>
  updateResource?: (id: string, data: any) => Promise<TResource>
}

// Controller configuration interface
export interface ControllerConfig<
  TResource extends ResourceWithUser,
  TFilters extends BaseFilters,
  TSort extends BaseSort,
  TService extends BaseResourceService<TResource, TFilters, TSort>,
> {
  service: TService
  resourceName: string
  validateQueryParams?: (request: any) => Promise<any>
  getStatusMapping?: () => Record<string, any> | undefined
  getSortMapping?: () => Record<string, any> | undefined
  applyCustomFilters?: (queryParams: any, filters: Partial<TFilters>) => void
  transformResource?: (resource: TResource) => any
  // Optional body validators for create/update endpoints
  validateCreatePayload?: (request: any) => Promise<any>
  validateUpdatePayload?: (request: any) => Promise<any>
}

/**
 * Validate user authentication
 */
export const validateAuthentication = async (auth: any, resourceName: string): Promise<string> => {
  if (!auth.user) {
    throw authorizationException(`You must be logged in to access ${resourceName}s`)
  }

  const userId = await auth.user.getUserId()
  if (!userId) {
    throw authorizationException(`You must be logged in to access ${resourceName}s`)
  }

  return userId
}

/**
 * Validate resource ownership
 */
export const validateResourceOwnership = async <TResource extends ResourceWithUser>(
  resourceId: string,
  userId: string,
  service: { getResourceById: (id: string) => Promise<TResource | null> },
  resourceName: string
): Promise<TResource> => {
  const resource = await service.getResourceById(resourceId)
  if (!resource) {
    throw notFoundException(`${resourceName} not found`)
  }
  if (resource.userId !== userId) {
    throw authorizationException(`You do not have permission to access this ${resourceName}`)
  }
  return resource
}

/**
 * Handle controller errors consistently
 */
export const handleControllerError = (error: any, response: any) => {
  if (error instanceof VineErrors.E_VALIDATION_ERROR) {
    return response.status(422).json(parseException(error))
  }
  const parsedError = parseException(error)
  // Ensure a message is always present
  if (!parsedError.message) {
    parsedError.message = error.message || 'An unexpected error occurred.'
  }
  return response.status(error.status || 400).json(parsedError)
}

/**
 * Transform query parameters to service format
 */
export const transformQueryParams = <TFilters extends BaseFilters, TSort extends BaseSort>(
  userId: string, // Add userId as a parameter
  queryParams: any,
  statusMapping?: Record<string, any>,
  sortMapping?: Record<string, any>,
  applyCustomFilters?: (queryParams: any, filters: Partial<TFilters>) => void
): BaseQueryParams<TFilters, TSort> => {
  const filters: Partial<TFilters> = { userId } as unknown as Partial<TFilters> // Include userId in filters

  // Apply common filters
  if (queryParams.search) filters.search = queryParams.search
  if (queryParams.startDate) filters.startDate = queryParams.startDate
  if (queryParams.endDate) filters.endDate = queryParams.endDate
  if (queryParams.status && statusMapping) {
    ;(filters as any).status = statusMapping[queryParams.status] || queryParams.status
  }

  // Add any additional custom filters
  if (applyCustomFilters) {
    applyCustomFilters(queryParams, filters)
  }

  return {
    filters: filters as TFilters,
    sort: queryParams.sortBy
      ? {
          field: sortMapping?.[queryParams.sortBy] || queryParams.sortBy,
          direction: (queryParams.sortDirection || 'desc') as 'asc' | 'desc',
        }
      : undefined,
    pagination: {
      page: queryParams.page || 1,
      limit: queryParams.limit || 20,
    },
  } as BaseQueryParams<TFilters, TSort>
}

/**
 * Standard index endpoint implementation
 */
export const createIndexHandler = <
  TResource extends ResourceWithUser,
  TFilters extends BaseFilters,
  TSort extends BaseSort,
  TService extends BaseResourceService<TResource, TFilters, TSort>,
>(
  config: ControllerConfig<TResource, TFilters, TSort, TService>
) => {
  return async ({ request, response, auth }: HttpContext) => {
    try {
      const userId = await validateAuthentication(auth, config.resourceName)

      const queryParams = config.validateQueryParams
        ? await config.validateQueryParams(request)
        : {}

      const transformedParams = transformQueryParams<TFilters, TSort>(
        userId, // Pass userId
        queryParams,
        config.getStatusMapping ? config.getStatusMapping() : undefined,
        config.getSortMapping ? config.getSortMapping() : undefined,
        config.applyCustomFilters
      )

      const result = await config.service.getResources(userId, {
        queryParams: transformedParams,
        paginate: true,
      })

      const transformedData = result.data.map((resource) =>
        config.transformResource ? config.transformResource(resource) : resource
      )

      const successResponse = createSuccessResponse({
        data: transformedData,
        pagination: result.pagination,
      })

      return response.status(200).json(successResponse)
    } catch (error) {
      return handleControllerError(error, response)
    }
  }
}

/**
 * Standard show endpoint implementation
 */
export const createShowHandler = <
  TResource extends ResourceWithUser,
  TFilters extends BaseFilters,
  TSort extends BaseSort,
  TService extends BaseResourceService<TResource, TFilters, TSort>,
>(
  config: ControllerConfig<TResource, TFilters, TSort, TService>,
  options?: {
    isResourceIdOptional?: boolean
    customResourceId?: string
  }
) => {
  return async ({ params, response, auth }: HttpContext) => {
    try {
      const userId = await validateAuthentication(auth, config.resourceName)
      const defOptions = {
        isResourceIdOptional: false,
        customResourceId: null,
      }
      const mergeOptions = { ...defOptions, ...options }
      const { id } = params
      const resourceId = mergeOptions.customResourceId || id
      if (!resourceId && !mergeOptions.isResourceIdOptional) {
        throw new Error(`${config.resourceName} ID is required`)
      }

      const resource = await validateResourceOwnership(
        resourceId,
        userId,
        config.service,
        config.resourceName
      )
      return response.status(200).json(
        createSuccessResponse({
          data: config.transformResource ? config.transformResource(resource) : resource,
        })
      )
    } catch (error) {
      return handleControllerError(error, response)
    }
  }
}

/**
 * Standard destroy endpoint implementation
 */
export const createDestroyHandler = <
  TResource extends ResourceWithUser,
  TFilters extends BaseFilters,
  TSort extends BaseSort,
  TService extends BaseResourceService<TResource, TFilters, TSort>,
>(
  config: ControllerConfig<TResource, TFilters, TSort, TService>
) => {
  return async ({ params, response, auth }: HttpContext) => {
    try {
      const userId = await validateAuthentication(auth, config.resourceName)
      const { id: resourceId } = params
      if (!resourceId) {
        throw new Error(`${config.resourceName} ID is required`)
      }

      await validateResourceOwnership(resourceId, userId, config.service, config.resourceName)
      const success = await config.service.deleteResource(resourceId, userId)

      if (!success) {
        throw notFoundException(`${config.resourceName} not found or access denied`)
      }

      return response.status(200).json(
        createSuccessResponse({
          data: null,
          message: `${config.resourceName} deleted successfully`,
        })
      )
    } catch (error) {
      return handleControllerError(error, response)
    }
  }
}

/**
 * Standard create (store) endpoint implementation
 */
export const createStoreHandler = <
  TResource extends ResourceWithUser,
  TFilters extends BaseFilters,
  TSort extends BaseSort,
  TService extends BaseResourceService<TResource, TFilters, TSort>,
>(
  config: ControllerConfig<TResource, TFilters, TSort, TService>
) => {
  return async ({ request, response, auth }: HttpContext) => {
    try {
      if (!config.service.createResource) {
        throw new Error(`createResource not implemented for ${config.resourceName}`)
      }

      const userId = await validateAuthentication(auth, config.resourceName)

      const payload = config.validateCreatePayload
        ? await config.validateCreatePayload(request)
        : request.body()

      const resource = await config.service.createResource(userId, payload)

      const transformedResource = config.transformResource
        ? config.transformResource(resource)
        : resource

      const successResponse = createSuccessResponse({
        data: transformedResource,
        message: `${config.resourceName} created successfully`,
      })

      return response.status(201).json(successResponse)
    } catch (error) {
      return handleControllerError(error, response)
    }
  }
}

/**
 * Standard update endpoint implementation
 */
export const createUpdateHandler = <
  TResource extends ResourceWithUser,
  TFilters extends BaseFilters,
  TSort extends BaseSort,
  TService extends BaseResourceService<TResource, TFilters, TSort>,
>(
  config: ControllerConfig<TResource, TFilters, TSort, TService>
) => {
  return async ({ params, request, response, auth }: HttpContext) => {
    try {
      if (!config.service.updateResource) {
        throw new Error(`updateResource not implemented for ${config.resourceName}`)
      }

      const userId = await validateAuthentication(auth, config.resourceName)

      const { id: resourceId } = params

      if (!resourceId) {
        throw new Error(`${config.resourceName} ID is required`)
      }

      // Ensure ownership before allowing update
      await validateResourceOwnership(resourceId, userId, config.service, config.resourceName)

      const payload = config.validateUpdatePayload
        ? await config.validateUpdatePayload(request)
        : request.body()

      const updatedResource = await config.service.updateResource(resourceId, payload)

      const transformedResource = config.transformResource
        ? config.transformResource(updatedResource)
        : updatedResource

      const successResponse = createSuccessResponse({
        data: transformedResource,
        message: `${config.resourceName} updated successfully`,
      })

      return response.status(200).json(successResponse)
    } catch (error) {
      return handleControllerError(error, response)
    }
  }
}
