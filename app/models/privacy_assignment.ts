import BaseModel from '#models/base_model'
import { column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { ActivityDetailVisibilityLevel } from '#types/policy_template_types'
import PrivacyPolicyTemplate from '#features/privacy-policy/template/privacy_policy_template_model'
import Task from '#features/task/task_model'
import Event from '#features/event/event_model'

export default class PrivacyAssignment extends BaseModel {
  static table = 'privacy_assignments'

  @column({ isPrimary: true })
  declare id: string

  // Nullable foreign keys - exactly one must be set
  @column()
  declare taskId: string | null

  @column()
  declare eventId: string | null

  @column()
  declare templateId: string

  // Override fields (optional, inherits from template if null)
  @column()
  declare overrideBlocksScheduling: boolean | null

  @column()
  declare overrideDetailVisibility: ActivityDetailVisibilityLevel | null

  @column()
  declare overrideCustomMessage: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => Task, {
    foreignKey: 'taskId',
  })
  declare task: BelongsTo<typeof Task>

  @belongsTo(() => Event, {
    foreignKey: 'eventId',
  })
  declare event: BelongsTo<typeof Event>

  @belongsTo(() => PrivacyPolicyTemplate, {
    foreignKey: 'templateId',
  })
  declare template: BelongsTo<typeof PrivacyPolicyTemplate>

  // Helper methods
  get resourceType(): 'task' | 'event' {
    if (this.taskId) return 'task'
    if (this.eventId) return 'event'
    throw new Error('PrivacyAssignment must have either taskId or eventId set')
  }

  get resourceId(): string {
    if (this.taskId) return this.taskId
    if (this.eventId) return this.eventId
    throw new Error('PrivacyAssignment must have either taskId or eventId set')
  }

  // Validation helpers
  get isTaskAssignment(): boolean {
    return this.taskId !== null
  }

  get isEventAssignment(): boolean {
    return this.eventId !== null
  }
}
