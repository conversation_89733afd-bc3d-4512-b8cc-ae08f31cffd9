import { inject } from '@adonisjs/core'
import UserGroup from './group_model.js'
import GroupMember from '../members/group_member_model.js'
import User from '#features/user/user_model'
import { CreateGroupData, GroupMemberRole } from '#types/group_types'
import type { BaseResourceService } from '#controllers/base_controller'
import type { ResourceWithUser, PaginatedResponse, BaseQueryParams } from '#types/base_query_types'
import { authorizationException, notFoundException } from '#utils/error'

// Adapter interface to make UserGroup compatible with base controller
interface GroupResource extends ResourceWithUser {
  id: string
  userId: string // Maps to ownerUserId
  ownerUserId: string
  name: string
  description: string | null
  createdAt: any
  updatedAt: any
}

/**
 * GroupService
 *
 * Handles business logic for core group management:
 * - Group creation, updates, and deletion
 * - Group querying and filtering
 * - Authorization checks for group operations
 */
@inject()
export default class GroupService implements BaseResourceService<GroupResource, any, any> {
  /**
   * Create a new group with the user as owner
   */
  async createResource(ownerUserId: string, data: CreateGroupData): Promise<GroupResource> {
    // Validate that the user exists
    const user = await User.find(ownerUserId)
    if (!user) {
      throw notFoundException('User not found')
    }

    // Use a transaction to ensure both group and member are created atomically
    const db = (await import('@adonisjs/lucid/services/db')).default

    const group = await db.transaction(async (trx) => {
      // Create the group
      const newGroup = await UserGroup.create(
        {
          ownerUserId,
          name: data.name,
          description: data.description || null,
        },
        { client: trx }
      )

      // Add the owner as a member with owner role
      await GroupMember.create(
        {
          groupId: newGroup.id,
          userId: ownerUserId,
          role: GroupMemberRole.OWNER,
        },
        { client: trx }
      )

      return newGroup
    })

    return this.mapToResource(group)
  }

  /**
   * Update group details (only owner can update)
   */
  async updateResource(groupId: string, data: any): Promise<GroupResource> {
    // The base controller will handle ownership validation
    // We just need to update the group
    const group = await this.getGroupById(groupId)

    // Update the group
    group.merge({
      name: data.name,
      description: data.description,
    })
    await group.save()

    return this.mapToResource(group)
  }

  /**
   * Delete a group (only owner can delete)
   */
  async deleteResource(groupId: string, userId: string): Promise<boolean> {
    const group = await this.getGroupById(groupId)

    // Check if user is the owner
    if (!group.isOwnedBy(userId)) {
      throw authorizationException('Only group owner can delete the group')
    }

    // Soft delete all related records
    await GroupMember.query().where('groupId', groupId).update({ deletedAt: new Date() })

    // Soft delete the group
    await group.delete()
    return true
  }

  /**
   * Get group by ID
   */
  async getResourceById(groupId: string): Promise<GroupResource | null> {
    const group = await UserGroup.find(groupId)
    return group ? this.mapToResource(group) : null
  }

  /**
   * Get group by ID (internal method)
   */
  async getGroupById(groupId: string): Promise<UserGroup> {
    const group = await UserGroup.find(groupId)
    if (!group) {
      throw notFoundException('Group not found')
    }
    return group
  }

  /**
   * Get groups for a user (groups they own or are members of)
   */
  async getResources(
    userId: string,
    options: { queryParams?: BaseQueryParams<any, any>; paginate?: boolean } = {}
  ): Promise<PaginatedResponse<GroupResource>> {
    // Get groups owned by user
    const ownedGroups = await UserGroup.query().where('ownerUserId', userId)

    // Get groups where user is a member
    const membershipGroupIds = await GroupMember.query().select('groupId').where('userId', userId)

    const memberGroups =
      membershipGroupIds.length > 0
        ? await UserGroup.query().whereIn(
            'id',
            membershipGroupIds.map((m) => m.groupId)
          )
        : []

    // Combine and deduplicate
    const allGroups = [...ownedGroups, ...memberGroups]
    const uniqueGroups = allGroups.filter(
      (group, index, self) => index === self.findIndex((g) => g.id === group.id)
    )

    // Apply additional filters
    let filteredGroups = uniqueGroups
    if (options.queryParams?.filters?.ownerId) {
      filteredGroups = filteredGroups.filter(
        (group) => group.ownerUserId === options.queryParams!.filters!.ownerId
      )
    }

    // Apply sorting
    if (options.queryParams?.sort?.field) {
      const direction = options.queryParams.sort.direction || 'asc'
      const field = options.queryParams.sort.field as keyof UserGroup

      filteredGroups.sort((a, b) => {
        const aValue = a[field]
        const bValue = b[field]

        // Handle null/undefined values
        if (aValue == null && bValue == null) return 0
        if (aValue == null) return direction === 'asc' ? -1 : 1
        if (bValue == null) return direction === 'asc' ? 1 : -1

        if (direction === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
        }
      })
    } else {
      filteredGroups.sort((a, b) => b.createdAt.toMillis() - a.createdAt.toMillis())
    }

    const mappedGroups = filteredGroups.map((group) => this.mapToResource(group))

    return {
      data: mappedGroups,
      pagination: undefined, // TODO: Implement pagination
    }
  }

  /**
   * Check if a user can perform an action on a group
   */
  async checkGroupPermission(
    groupId: string,
    userId: string,
    action: 'view' | 'update' | 'delete'
  ): Promise<boolean> {
    try {
      const group = await this.getGroupById(groupId)

      // Owner can do everything
      if (group.isOwnedBy(userId)) {
        return true
      }

      // For other actions, check membership
      const member = await GroupMember.query()
        .where('groupId', groupId)
        .where('userId', userId)
        .first()

      if (!member) {
        return false
      }

      switch (action) {
        case 'view':
          return true // Any member can view
        case 'update':
          return member.canUpdateGroup()
        case 'delete':
          return member.canDeleteGroup()
        default:
          return false
      }
    } catch {
      return false
    }
  }

  /**
   * Map UserGroup to GroupResource for base controller compatibility
   */
  private mapToResource(group: UserGroup): GroupResource {
    return {
      id: group.id,
      userId: group.ownerUserId, // Map ownerUserId to userId for base controller
      ownerUserId: group.ownerUserId,
      name: group.name,
      description: group.description,
      createdAt: group.createdAt,
      updatedAt: group.updatedAt,
    }
  }
}
