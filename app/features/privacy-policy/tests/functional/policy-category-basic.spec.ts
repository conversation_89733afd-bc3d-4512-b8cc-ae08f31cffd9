import { test } from '@japa/runner'
import { ApiClient } from '@japa/api-client'
import db from '@adonisjs/lucid/services/db'
import { createTestUserWithToken } from '#tests/utils/test-user-factory'

test.group('Policy Category Basic', (group) => {
  group.setup(async () => {
    await db.beginGlobalTransaction()
    return () => db.rollbackGlobalTransaction()
  })

  // Helper function to create a test user (participates in database transactions)
  async function createTestUser(prefix: string = 'user') {
    const testUser = await createTestUserWithToken(prefix)
    return {
      authToken: testUser.token,
      userId: testUser.userId,
    }
  }

  // Helper function to create a test policy category
  async function createTestPolicyCategory(client: ApiClient, authToken: string, categoryData = {}) {
    const defaultCategoryData = {
      name: 'Work',
      description: 'Work-related privacy policies',
      ...categoryData,
    }

    const response = await client
      .post('/api/v1/privacy-profile-categories')
      .bearerToken(authToken)
      .json(defaultCategoryData)

    return { response, categoryData: defaultCategoryData }
  }

  test('user can create policy category', async ({ client, assert }) => {
    const { authToken } = await createTestUser('categoryuser')

    const categoryData = {
      name: 'Personal',
      description: 'Personal privacy settings for family and friends',
    }

    const response = await client
      .post('/api/v1/privacy-profile-categories')
      .bearerToken(authToken)
      .json(categoryData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.label, categoryData.name)
    assert.equal(body.data.key, 'personal') // Auto-generated key
    assert.equal(body.data.description, categoryData.description)
    assert.exists(body.data.id)
    assert.exists(body.data.createdAt)
    assert.exists(body.data.updatedAt)
  }).timeout(10000)

  test('user can create minimal policy category with only name', async ({ client, assert }) => {
    const { authToken } = await createTestUser('minimalcategoryuser')

    const categoryData = {
      name: 'Public',
    }

    const response = await client
      .post('/api/v1/privacy-profile-categories')
      .bearerToken(authToken)
      .json(categoryData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.data.label, categoryData.name)
    assert.equal(body.data.key, 'public')
    assert.isNull(body.data.description)
  }).timeout(10000)

  test('policy category creation validates required fields', async ({ client, assert }) => {
    const { authToken } = await createTestUser('validationcategoryuser')

    // Test missing name
    const response1 = await client
      .post('/api/v1/privacy-profile-categories')
      .bearerToken(authToken)
      .json({})

    response1.assertStatus(422) // Validation error

    // Test name too long
    const response2 = await client
      .post('/api/v1/privacy-profile-categories')
      .bearerToken(authToken)
      .json({
        name: 'a'.repeat(256), // Exceeds 255 character limit
      })

    response2.assertStatus(422)

    // Test empty name
    const response3 = await client
      .post('/api/v1/privacy-profile-categories')
      .bearerToken(authToken)
      .json({
        name: '',
      })

    response3.assertStatus(422)
  }).timeout(10000)

  test('user can get specific policy category by ID', async ({ client, assert }) => {
    const { authToken } = await createTestUser('getcategoryuser')

    // Create a policy category first
    const { response: createResponse } = await createTestPolicyCategory(client, authToken)
    createResponse.assertStatus(201)
    const categoryId = createResponse.body().data.id

    // Get the policy category
    const response = await client
      .get(`/api/v1/privacy-profile-categories/${categoryId}`)
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.id, categoryId)
    assert.equal(body.data.label, 'Work')
    assert.equal(body.data.description, 'Work-related privacy policies')
  }).timeout(10000)

  test('user cannot access other users policy categories', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('categoryuser1')
    const { authToken: user2Token } = await createTestUser('categoryuser2')

    // User 1 creates a policy category
    const { response: createResponse } = await createTestPolicyCategory(client, user1Token)
    createResponse.assertStatus(201)
    const categoryId = createResponse.body().data.id

    // User 2 tries to access User 1's policy category
    const response = await client
      .get(`/api/v1/privacy-profile-categories/${categoryId}`)
      .bearerToken(user2Token)

    response.assertStatus(403) // Forbidden (due to ownership check)
  }).timeout(10000)

  test('user can list their policy categories', async ({ client, assert }) => {
    const { authToken } = await createTestUser('listcategoryuser')

    // Create multiple policy categories
    await createTestPolicyCategory(client, authToken, { name: 'Work' })
    await createTestPolicyCategory(client, authToken, { name: 'Personal' })
    await createTestPolicyCategory(client, authToken, { name: 'Public' })

    const response = await client.get('/api/v1/privacy-profile-categories').bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.exists(body.pagination)
    assert.isArray(body.data)
    assert.isAtLeast(body.data.length, 3)
  }).timeout(15000)

  test('policy category listing supports pagination', async ({ client, assert }) => {
    const { authToken } = await createTestUser('paginationcategoryuser')

    // Create multiple policy categories
    for (let i = 1; i <= 5; i++) {
      await createTestPolicyCategory(client, authToken, { name: `Category ${i}` })
    }

    // Test pagination
    const response = await client
      .get('/api/v1/privacy-profile-categories')
      .bearerToken(authToken)
      .qs({ page: 1, limit: 2 })

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.data.length, 2)
    assert.exists(body.pagination)
    assert.equal(body.pagination.currentPage, 1) // Use currentPage instead of page
    assert.isAtLeast(body.pagination.totalCount, 5) // Use totalCount instead of total
  }).timeout(20000)

  test('policy category listing supports search', async ({ client, assert }) => {
    const { authToken } = await createTestUser('searchcategoryuser')

    // Create policy categories with searchable content
    await createTestPolicyCategory(client, authToken, {
      name: 'Work Projects',
      description: 'Business related policies',
    })
    await createTestPolicyCategory(client, authToken, {
      name: 'Family Time',
      description: 'Personal family policies',
    })

    // Search by name
    const response = await client
      .get('/api/v1/privacy-profile-categories')
      .bearerToken(authToken)
      .qs({ search: 'Work' })

    response.assertStatus(200)
    const body = response.body()

    assert.isAtLeast(body.data.length, 1)
    // Should find the work category
    const foundCategory = body.data.find((category: any) => category.label.includes('Work'))
    assert.exists(foundCategory)
  }).timeout(15000)

  test('policy category listing supports sorting', async ({ client, assert }) => {
    const { authToken } = await createTestUser('sortcategoryuser')

    // Create policy categories with different names
    await createTestPolicyCategory(client, authToken, { name: 'Zebra Category' })
    await createTestPolicyCategory(client, authToken, { name: 'Alpha Category' })

    // Sort by name ascending
    const response = await client
      .get('/api/v1/privacy-profile-categories')
      .bearerToken(authToken)
      .qs({ sortBy: 'name', sortDirection: 'asc' })

    response.assertStatus(200)
    const body = response.body()

    assert.isAtLeast(body.data.length, 2)
    // First category should be Alpha (alphabetically first)
    assert.equal(body.data[0].label, 'Alpha Category')
  }).timeout(15000)

  test('user can update policy category', async ({ client, assert }) => {
    const { authToken } = await createTestUser('updatecategoryuser')

    // Create a policy category first
    const { response: createResponse } = await createTestPolicyCategory(client, authToken)
    createResponse.assertStatus(201)
    const categoryId = createResponse.body().data.id

    // Update the policy category
    const updateData = {
      name: 'Updated Work Category',
      description: 'Updated work-related privacy policies',
    }

    const response = await client
      .put(`/api/v1/privacy-profile-categories/${categoryId}`)
      .bearerToken(authToken)
      .json(updateData)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.data.label, updateData.name)
    assert.equal(body.data.key, 'updated_work_category')
    assert.equal(body.data.description, updateData.description)
    assert.equal(body.data.id, categoryId)
  }).timeout(10000)

  test('user can partially update policy category', async ({ client, assert }) => {
    const { authToken } = await createTestUser('partialupdatecategoryuser')

    // Create a policy category first
    const { response: createResponse, categoryData } = await createTestPolicyCategory(
      client,
      authToken
    )
    createResponse.assertStatus(201)
    const categoryId = createResponse.body().data.id

    // Partial update - only name
    const updateData = {
      name: 'Partially Updated Name',
    }

    const response = await client
      .put(`/api/v1/privacy-profile-categories/${categoryId}`)
      .bearerToken(authToken)
      .json(updateData)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.data.label, updateData.name)
    assert.equal(body.data.key, 'partially_updated_name')
    // Description should remain unchanged
    assert.equal(body.data.description, categoryData.description)
  }).timeout(10000)

  test('user cannot update other users policy categories', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('updatecategoryuser1')
    const { authToken: user2Token } = await createTestUser('updatecategoryuser2')

    // User 1 creates a policy category
    const { response: createResponse } = await createTestPolicyCategory(client, user1Token)
    createResponse.assertStatus(201)
    const categoryId = createResponse.body().data.id

    // User 2 tries to update User 1's policy category
    const response = await client
      .put(`/api/v1/privacy-profile-categories/${categoryId}`)
      .bearerToken(user2Token)
      .json({ name: 'Hacked Category' })

    response.assertStatus(403) // Forbidden (due to ownership check)
  }).timeout(10000)

  test('user can delete policy category', async ({ client, assert }) => {
    const { authToken } = await createTestUser('deletecategoryuser')

    // Create a policy category first
    const { response: createResponse } = await createTestPolicyCategory(client, authToken)
    createResponse.assertStatus(201)
    const categoryId = createResponse.body().data.id

    // Delete the policy category
    const response = await client
      .delete(`/api/v1/privacy-profile-categories/${categoryId}`)
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.message)

    // Verify policy category is deleted (should return 404)
    const getResponse = await client
      .get(`/api/v1/privacy-profile-categories/${categoryId}`)
      .bearerToken(authToken)

    getResponse.assertStatus(404)
  }).timeout(10000)

  test('user cannot delete other users policy categories', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('deletecategoryuser1')
    const { authToken: user2Token } = await createTestUser('deletecategoryuser2')

    // User 1 creates a policy category
    const { response: createResponse } = await createTestPolicyCategory(client, user1Token)
    createResponse.assertStatus(201)
    const categoryId = createResponse.body().data.id

    // User 2 tries to delete User 1's policy category
    const response = await client
      .delete(`/api/v1/privacy-profile-categories/${categoryId}`)
      .bearerToken(user2Token)

    response.assertStatus(403) // Forbidden (due to ownership check)
  }).timeout(10000)

  test('policy category operations require authentication', async ({ client, assert }) => {
    // Test all endpoints without authentication
    const response1 = await client.get('/api/v1/privacy-profile-categories')
    response1.assertStatus(401)

    const response2 = await client.post('/api/v1/privacy-profile-categories').json({ name: 'Test' })
    response2.assertStatus(401)

    const response3 = await client.get('/api/v1/privacy-profile-categories/123')
    response3.assertStatus(401)

    const response4 = await client
      .put('/api/v1/privacy-profile-categories/123')
      .json({ name: 'Test' })
    response4.assertStatus(401)

    const response5 = await client.delete('/api/v1/privacy-profile-categories/123')
    response5.assertStatus(401)
  }).timeout(10000)

  test('key generation handles special characters', async ({ client, assert }) => {
    const { authToken } = await createTestUser('keygenerationuser')

    const categoryData = {
      name: 'Work & Business 123!',
      description: 'Category with special characters',
    }

    const response = await client
      .post('/api/v1/privacy-profile-categories')
      .bearerToken(authToken)
      .json(categoryData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.data.label, categoryData.name)
    // Key should be sanitized
    assert.equal(body.data.key, 'work_&_business_123!')
  }).timeout(10000)
})
