import PrivacyPolicyTemplate from './privacy_policy_template_model.js'
import PolicyCategory from '../category/policy_category_model.js'
import User from '../../user/user_model.js'
import type { ModelAttributes } from '@adonisjs/lucid/types/model'
import type { NonPatchableModelFields } from '#types/model_utilities'
import { notFoundException } from '#utils/error'
import { ActivityDetailVisibilityLevel } from '#types/policy_template_types'
import { BaseResourceService } from '#controllers/base_controller'
import { PrivacyPolicyTemplateFilters, PrivacyPolicyTemplateSort } from '../policy_types.js'
import { PaginatedResponse } from '#types/base_query_types'
import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

// Derive patchable attributes from the PrivacyPolicyTemplate model
type AllowedPrivacyPolicyTemplatePatchAttributes = Omit<
  ModelAttributes<PrivacyPolicyTemplate>,
  NonPatchableModelFields
>

// PrivacyPolicyTemplateForMerge: Represents the data structure for merging user input during privacy policy template updates.
// It excludes critical fields that should not be directly modifiable via user input.
type PrivacyPolicyTemplatePatchData = Partial<AllowedPrivacyPolicyTemplatePatchAttributes>

// Extended interface for PrivacyPolicyTemplate with guaranteed userId (for base controller compatibility)
interface PrivacyPolicyTemplateWithUser extends PrivacyPolicyTemplate {
  userId: string
}

/**
 * PrivacyPolicyTemplateService
 *
 * Service for managing privacy policy templates including CRUD operations
 * and system-wide template management.
 */
export default class PrivacyPolicyTemplateService
  implements
    BaseResourceService<
      PrivacyPolicyTemplateWithUser,
      PrivacyPolicyTemplateFilters,
      PrivacyPolicyTemplateSort
    >
{
  /**
   * Create a new privacy policy template for a user
   */
  async create(
    userId: string,
    templateData: {
      name: string
      description?: string | null
      categoryId?: string | null
      policyCategoryId?: string | null // API field mapping
      permissions?: {
        canViewCalendar?: boolean
        canScheduleMeeting?: boolean
        showDetails?: boolean
      }
      isDefault?: boolean
      isSystemTemplate?: boolean
      blocksScheduling?: boolean
      defaultDetailVisibility?: ActivityDetailVisibilityLevel
      defaultCustomMessage?: string | null
    }
  ): Promise<PrivacyPolicyTemplate> {
    // Verify user exists
    await User.findOrFail(userId)

    // Handle API field mapping: policyCategoryId -> categoryId
    const categoryId = templateData.policyCategoryId || templateData.categoryId

    // If a category is specified, verify it exists and belongs to the user or is a system category
    if (categoryId) {
      const category = await PolicyCategory.query()
        .where('id', categoryId)
        .where((query) => {
          query.where('userId', userId).orWhereNull('userId')
        })
        .first()

      if (!category) {
        throw notFoundException('Policy category not found or access denied')
      }
    }

    // Handle permissions mapping to internal fields
    let blocksScheduling = templateData.blocksScheduling ?? false
    let defaultDetailVisibility =
      templateData.defaultDetailVisibility ?? ActivityDetailVisibilityLevel.HIDDEN

    if (templateData.permissions) {
      // If canScheduleMeeting is false, then it blocks scheduling
      if (templateData.permissions.canScheduleMeeting !== undefined) {
        blocksScheduling = !templateData.permissions.canScheduleMeeting
      }

      // Map canViewCalendar and showDetails to defaultDetailVisibility
      // Priority: showDetails > canViewCalendar
      if (templateData.permissions.showDetails !== undefined) {
        if (templateData.permissions.showDetails) {
          defaultDetailVisibility = ActivityDetailVisibilityLevel.FULL_DETAILS
        } else {
          // showDetails is false, but check canViewCalendar for intermediate level
          defaultDetailVisibility = templateData.permissions.canViewCalendar
            ? ActivityDetailVisibilityLevel.BUSY_ONLY
            : ActivityDetailVisibilityLevel.HIDDEN
        }
      } else if (templateData.permissions.canViewCalendar !== undefined) {
        // If canViewCalendar is false, set to hidden; otherwise set to busy_only (default)
        defaultDetailVisibility = templateData.permissions.canViewCalendar
          ? ActivityDetailVisibilityLevel.BUSY_ONLY
          : ActivityDetailVisibilityLevel.HIDDEN
      }
    }

    const template = await PrivacyPolicyTemplate.create({
      userId,
      name: templateData.name,
      description: templateData.description || null,
      categoryId: categoryId || null,
      isDefault: templateData.isDefault ?? false,
      isSystemTemplate: templateData.isSystemTemplate ?? false,
      blocksScheduling,
      defaultDetailVisibility,
      defaultCustomMessage: templateData.defaultCustomMessage || null,
    })

    return template
  }

  /**
   * Get privacy policy templates for a specific user
   */
  async getByUserId(userId: string) {
    return PrivacyPolicyTemplate.query()
      .where('userId', userId)
      .preload('category')
      .orderBy('name', 'asc')
  }

  /**
   * Get a privacy policy template by ID (optionally filtered by user)
   */
  async getById(id: string, userId?: string) {
    const query = PrivacyPolicyTemplate.query().where('id', id).preload('category').preload('rules')

    if (userId) {
      query.where('userId', userId)
    }

    const template = await query.first()
    if (!template) {
      throw notFoundException('Privacy policy template not found')
    }

    return template
  }

  /**
   * Update a privacy policy template
   */
  async update(id: string, userId: string, data: PrivacyPolicyTemplatePatchData) {
    const template = await PrivacyPolicyTemplate.query()
      .where('id', id)
      .where('userId', userId)
      .first()

    if (!template) {
      throw notFoundException('Privacy policy template not found')
    }

    // If updating category, verify it exists and belongs to the user or is a system category
    if (data.categoryId !== undefined) {
      if (data.categoryId !== null) {
        const category = await PolicyCategory.query()
          .where('id', data.categoryId)
          .where((query) => {
            query.where('userId', userId).orWhereNull('userId')
          })
          .first()

        if (!category) {
          throw notFoundException('Policy category not found or access denied')
        }
      }
    }

    template.merge(data)
    await template.save()

    return template
  }

  /**
   * Delete a privacy policy template
   */
  async delete(id: string, userId: string) {
    const template = await PrivacyPolicyTemplate.query()
      .where('id', id)
      .where('userId', userId)
      .first()

    if (!template) {
      throw notFoundException('Privacy policy template not found')
    }

    await template.delete()
    return true
  }

  /**
   * Get system-wide privacy policy templates
   */
  async getSystemTemplates() {
    return PrivacyPolicyTemplate.query()
      .whereNull('userId')
      .where('isSystemTemplate', true)
      .preload('category')
      .orderBy('name', 'asc')
  }

  /**
   * Get default privacy policy template for a user
   */
  async getDefaultTemplate(userId: string) {
    return PrivacyPolicyTemplate.query()
      .where('userId', userId)
      .where('isDefault', true)
      .preload('category')
      .preload('rules')
      .first()
  }

  // Base interface implementation methods

  /**
   * Get resource by ID (base interface implementation)
   */
  async getResourceById(id: string): Promise<PrivacyPolicyTemplateWithUser | null> {
    const template = await PrivacyPolicyTemplate.find(id)
    // Only return templates with valid userId (not system templates)
    if (template && template.userId) {
      return template as PrivacyPolicyTemplateWithUser
    }
    return null
  }

  /**
   * Get resources with pagination (base interface implementation)
   */
  async getResources(
    userId: string,
    options: { queryParams?: any; paginate?: boolean }
  ): Promise<PaginatedResponse<PrivacyPolicyTemplateWithUser>> {
    const { queryParams = {}, paginate = true } = options
    const { filters, sort, pagination } = queryParams

    let query = PrivacyPolicyTemplate.query().where('userId', userId).preload('category')

    if (filters) {
      query = this.applyFilters(query, filters)
    }

    if (sort) {
      query = this.applySorting(query, sort)
    } else {
      query = query.orderBy('name', 'asc')
    }

    if (!paginate) {
      const templates = await query

      const result = {
        data: templates as PrivacyPolicyTemplateWithUser[],
        pagination: undefined,
      }
      return result
    }

    const { page = 1, limit = 20 } = pagination || {}

    const paginatedResult = await query.paginate(page, limit)

    const result = {
      data: paginatedResult.all() as PrivacyPolicyTemplateWithUser[],
      pagination: {
        currentPage: paginatedResult.currentPage,
        totalPages: paginatedResult.lastPage,
        totalCount: paginatedResult.total,
        hasNextPage: paginatedResult.hasMorePages,
      },
    }

    return result
  }

  /**
   * Delete resource (base interface implementation)
   */
  async deleteResource(id: string, userId: string): Promise<boolean> {
    try {
      return await this.delete(id, userId)
    } catch {
      return false
    }
  }

  /**
   * Update resource (base interface implementation)
   */
  async updateResource(id: string, data: any): Promise<PrivacyPolicyTemplateWithUser> {
    // Get the template to find the userId (ownership was already validated by base controller)
    const template = await PrivacyPolicyTemplate.findOrFail(id)

    const userId = template.userId

    // Handle API field mapping: policyCategoryId -> categoryId
    const updateData: PrivacyPolicyTemplatePatchData = { ...data }

    if (data.policyCategoryId !== undefined) {
      updateData.categoryId = data.policyCategoryId
      delete (updateData as any).policyCategoryId
    }

    // Handle permissions mapping to internal fields
    if (data.permissions) {
      if (data.permissions.canScheduleMeeting !== undefined) {
        updateData.blocksScheduling = !data.permissions.canScheduleMeeting
      }

      // Map canViewCalendar and showDetails to defaultDetailVisibility
      // Priority: showDetails > canViewCalendar
      if (data.permissions.showDetails !== undefined) {
        updateData.defaultDetailVisibility = data.permissions.showDetails
          ? ActivityDetailVisibilityLevel.FULL_DETAILS
          : data.permissions.canViewCalendar === false
            ? ActivityDetailVisibilityLevel.HIDDEN
            : ActivityDetailVisibilityLevel.BUSY_ONLY
      } else if (data.permissions.canViewCalendar !== undefined) {
        // If canViewCalendar is false, set to hidden; otherwise set to busy_only (default)
        updateData.defaultDetailVisibility = data.permissions.canViewCalendar
          ? ActivityDetailVisibilityLevel.BUSY_ONLY
          : ActivityDetailVisibilityLevel.HIDDEN
      }

      delete (updateData as any).permissions
    }

    const updatedTemplate = await this.update(id, userId, updateData)

    return updatedTemplate as PrivacyPolicyTemplateWithUser
  }

  /**
   * Create resource (base interface implementation)
   */
  async createResource(userId: string, data: any): Promise<PrivacyPolicyTemplateWithUser> {
    const template = await this.create(userId, data)
    return template as PrivacyPolicyTemplateWithUser
  }

  private applyFilters(
    query: ModelQueryBuilderContract<typeof PrivacyPolicyTemplate>,
    filters: PrivacyPolicyTemplateFilters
  ): ModelQueryBuilderContract<typeof PrivacyPolicyTemplate> {
    if (filters.search) {
      query = query.where((subQuery) => {
        subQuery
          .whereILike('name', `%${filters.search}%`)
          .orWhereILike('description', `%${filters.search}%`)
      })
    }

    if (filters.isDefault !== undefined) {
      query = query.where('isDefault', filters.isDefault)
    }

    if (filters.policyCategory) {
      query = query.where('categoryId', filters.policyCategory)
    }

    return query
  }

  private applySorting(
    query: ModelQueryBuilderContract<typeof PrivacyPolicyTemplate>,
    sort: PrivacyPolicyTemplateSort
  ): ModelQueryBuilderContract<typeof PrivacyPolicyTemplate> {
    const { field, direction } = sort

    switch (field) {
      case 'name':
        query = query.orderBy('name', direction)
        break
      case 'isDefault':
        query = query.orderBy('isDefault', direction)
        break
      case 'createdAt':
        query = query.orderBy('createdAt', direction)
        break
      case 'updatedAt':
        query = query.orderBy('updatedAt', direction)
        break
      default:
        query = query.orderBy('name', direction)
    }

    return query
  }
}
