import type { HttpContext } from '@adonisjs/core/http'
import PrivacyPolicyTemplateService from './privacy_policy_template_service.js'
import {
  privacyPolicyTemplateQueryValidator,
  createPrivacyPolicyTemplateValidator,
  updatePrivacyPolicyTemplateValidator,
} from '../policy_validators.js'
import { createSuccessResponse } from '#utils/response'
import { PrivacyPolicyTemplateFilters } from '../policy_types.js'

export default class PrivacyPolicyTemplateController {
  private service = new PrivacyPolicyTemplateService()
  private baseController: any = null

  /**
   * Transform template data for API response
   */
  private transformTemplate(template: any) {
    // If template has serialize method (Lucid model), use it, otherwise use as-is
    const cleanTemplate = template.serialize ? template.serialize() : template

    return {
      id: cleanTemplate.id,
      userId: cleanTemplate.userId,
      name: cleanTemplate.name,
      description: cleanTemplate.description,
      policyCategoryId: cleanTemplate.categoryId, // Map categoryId to policyCategoryId for API consistency
      isDefault: cleanTemplate.isDefault,
      isSystemTemplate: cleanTemplate.isSystemTemplate,
      createdAt: cleanTemplate.createdAt,
      updatedAt: cleanTemplate.updatedAt,
      permissions: {
        // Map internal fields to expected permissions structure
        // canViewCalendar: derive from detail visibility - if not hidden, calendar can be viewed
        canViewCalendar: cleanTemplate.defaultDetailVisibility !== 'hidden',
        // canScheduleMeeting: inverse of blocksScheduling
        canScheduleMeeting: !cleanTemplate.blocksScheduling,
        // showDetails: true if full details are visible
        showDetails: cleanTemplate.defaultDetailVisibility === 'full_details',
      },
    }
  }

  /**
   * Initialize base controller dynamically for HMR compatibility
   */
  private async initBaseController() {
    if (!this.baseController) {
      const baseControllerModule = await import('#controllers/base_controller')
      this.baseController = baseControllerModule
    }
    return this.baseController
  }

  /**
   * Configuration for base controller utilities
   */
  private async getConfig() {
    await this.initBaseController()
    return {
      service: this.service,
      resourceName: 'privacy policy template',
      validateQueryParams: async (request: any) =>
        await request.validateUsing(privacyPolicyTemplateQueryValidator),
      getStatusMapping: () => undefined, // No status field for privacy policy templates
      getSortMapping: () => ({
        name: 'name',
        isDefault: 'isDefault',
        createdAt: 'createdAt',
        updatedAt: 'updatedAt',
      }),
      applyCustomFilters: (queryParams: any, filters: Partial<PrivacyPolicyTemplateFilters>) => {
        if (queryParams.isDefault !== undefined) {
          filters.isDefault = queryParams.isDefault
        }
        if (queryParams.policyCategory) {
          filters.policyCategory = queryParams.policyCategory
        }
      },
      transformResource: (template: any) => this.transformTemplate(template),
      validateCreatePayload: async (request: any) => {
        return await request.validateUsing(createPrivacyPolicyTemplateValidator)
      },
      validateUpdatePayload: async (request: any) => {
        return await request.validateUsing(updatePrivacyPolicyTemplateValidator)
      },
    }
  }

  /**
   * List all privacy policy templates for the authenticated user with filtering, sorting, and pagination
   */
  index = async (ctx: HttpContext) => {
    try {
      const base = await this.initBaseController()

      // Manually implement the index logic to get proper response structure
      const userId = await base.validateAuthentication(ctx.auth, 'privacy policy template')

      const config = await this.getConfig()
      const queryParams = config.validateQueryParams
        ? await config.validateQueryParams(ctx.request)
        : {}

      const transformedParams = base.transformQueryParams(
        userId,
        queryParams,
        config.getStatusMapping ? config.getStatusMapping() : undefined,
        config.getSortMapping ? config.getSortMapping() : undefined,
        config.applyCustomFilters
      )

      const result = await config.service.getResources(userId, {
        queryParams: transformedParams,
        paginate: true,
      })

      const transformedData = result.data.map((resource) =>
        config.transformResource ? config.transformResource(resource) : resource
      )

      // Create response structure that matches test expectations: body.data.data and body.data.pagination
      // Transform pagination to match test expectations (currentPage -> page, add perPage)
      const transformedPagination = result.pagination
        ? {
            page: result.pagination.currentPage,
            perPage: transformedParams.pagination?.limit || 20,
            total: result.pagination.totalCount,
            totalPages: result.pagination.totalPages,
            hasNextPage: result.pagination.hasNextPage,
            hasPreviousPage: result.pagination.hasPreviousPage,
          }
        : undefined

      const response = createSuccessResponse({
        data: {
          data: transformedData,
          pagination: transformedPagination,
        },
      })

      return ctx.response.status(200).json(response)
    } catch (error) {
      const base = await this.initBaseController()
      return base.handleControllerError(error, ctx.response)
    }
  }

  /**
   * Get a specific privacy policy template by ID
   */
  show = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createShowHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Delete a privacy policy template (soft delete)
   */
  destroy = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createDestroyHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Create a new privacy policy template
   */
  store = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const config = await this.getConfig()
    const handler = base.createStoreHandler(config)
    return await handler(ctx)
  }

  /**
   * Update an existing privacy policy template
   */
  update = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const config = await this.getConfig()
    const handler = base.createUpdateHandler(config)
    return await handler(ctx)
  }
}
