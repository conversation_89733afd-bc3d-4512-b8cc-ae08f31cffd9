import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import CommitmentService from './commitment_service.js'
import { createSuccessResponse } from '#utils/response'
import { validateAuthentication, handleControllerError } from '#controllers/base_controller'
import { createCommitmentValidator, respondToCommitmentValidator } from './commitment_validator.js'
import {
  CreateCommitmentData,
  RespondToCommitmentData,
} from '#features/commitment/types/commitment_types'

/**
 * CommitmentController
 *
 * Handles HTTP requests for commitment management:
 * - Creating invitations for tasks and events
 * - Responding to invitations
 * - Cancelling invitations
 * - Listing invitations (received/sent)
 */
@inject()
export default class CommitmentController {
  constructor(private commitmentService: CommitmentService) { }

  /**
   * Create a task invitation
   * POST /api/v1/tasks/:id/invitations
   */
  async storeTaskInvitation({ params, request, response, auth }: HttpContext) {
    try {
      const userId = await validateAuthentication(auth, 'commitment')
      const { id: taskId } = params

      if (!taskId) {
        throw new Error('Task ID is required')
      }

      const payload = await request.validateUsing(createCommitmentValidator)

      const data: CreateCommitmentData = {
        inviteeUserId: payload.inviteeUserId,
        message: payload.message,
      }

      const commitment = await this.commitmentService.createTaskInvitation(taskId, userId, data)

      return response.created(
        createSuccessResponse({
          data: commitment,
          message: 'Task invitation sent successfully',
        })
      )
    } catch (error) {
      return handleControllerError(error, response)
    }
  }

  /**
   * Create an event invitation
   * POST /api/v1/events/:id/invitations
   */
  async storeEventInvitation({ params, request, response, auth }: HttpContext) {
    try {
      const userId = await validateAuthentication(auth, 'commitment')
      const { id: eventId } = params

      if (!eventId) {
        throw new Error('Event ID is required')
      }
      const payload = await request.validateUsing(createCommitmentValidator)

      const data: CreateCommitmentData = {
        inviteeUserId: payload.inviteeUserId,
        message: payload.message,
      }

      const commitment = await this.commitmentService.createEventInvitation(eventId, userId, data)

      return response.created(
        createSuccessResponse({
          data: commitment,
          message: 'Event invitation sent successfully',
        })
      )
    } catch (error) {
      return handleControllerError(error, response)
    }
  }

  /**
   * Respond to a commitment invitation
   * PUT /api/v1/invitations/:id
   */
  async update({ params, request, response, auth }: HttpContext) {
    try {
      const userId = await validateAuthentication(auth, 'commitment')
      const { id: commitmentId } = params

      if (!commitmentId) {
        throw new Error('Commitment ID is required')
      }
      const payload = await request.validateUsing(respondToCommitmentValidator)

      const data: RespondToCommitmentData = {
        status: payload.status,
      }

      const commitment = await this.commitmentService.respondToCommitment(commitmentId, userId, data)

      const action = payload.status === 'accepted' ? 'accepted' : 'declined'
      const message = `Invitation ${action} successfully`

      return response.ok(
        createSuccessResponse({
          data: commitment,
          message,
        })
      )
    } catch (error) {
      return handleControllerError(error, response)
    }
  }

  /**
   * Cancel a commitment invitation (host only)
   * DELETE /api/v1/invitations/:id
   */
  async destroy({ params, response, auth }: HttpContext) {
    try {
      const userId = await validateAuthentication(auth, 'commitment')
      const { id: commitmentId } = params

      if (!commitmentId) {
        throw new Error('Commitment ID is required')
      }

      const commitment = await this.commitmentService.cancelCommitment(commitmentId, userId)

      return response.ok(
        createSuccessResponse({
          data: commitment,
          message: 'Invitation cancelled successfully',
        })
      )
    } catch (error) {
      return handleControllerError(error, response)
    }
  }

  /**
   * Get invitations received by the authenticated user
   * GET /api/v1/invitations/received
   */
  async indexReceived({ response, auth }: HttpContext) {
    try {
      const userId = await validateAuthentication(auth, 'commitment')

      const invitations = await this.commitmentService.getReceivedInvitations(userId)

      return response.ok(
        createSuccessResponse({
          data: invitations,
          message: 'Received invitations retrieved successfully',
        })
      )
    } catch (error) {
      return handleControllerError(error, response)
    }
  }

  /**
   * Get invitations sent by the authenticated user
   * GET /api/v1/invitations/sent
   */
  async indexSent({ response, auth }: HttpContext) {
    try {
      const userId = await validateAuthentication(auth, 'commitment')

      const invitations = await this.commitmentService.getSentInvitations(userId)

      return response.ok(
        createSuccessResponse({
          data: invitations,
          message: 'Sent invitations retrieved successfully',
        })
      )
    } catch (error) {
      return handleControllerError(error, response)
    }
  }

  /**
   * Get commitments for a specific task
   * GET /api/v1/tasks/:id/invitations
   */
  async indexTaskCommitments({ params, response, auth }: HttpContext) {
    try {
      const userId = await validateAuthentication(auth, 'commitment')
      const { id: taskId } = params

      if (!taskId) {
        throw new Error('Task ID is required')
      }

      const commitments = await this.commitmentService.getTaskCommitments(taskId, userId)

      return response.ok(
        createSuccessResponse({
          data: commitments,
          message: 'Task commitments retrieved successfully',
        })
      )
    } catch (error) {
      return handleControllerError(error, response)
    }
  }

  /**
   * Get commitments for a specific event
   * GET /api/v1/events/:id/invitations
   */
  async indexEventCommitments({ params, response, auth }: HttpContext) {
    try {
      const userId = await validateAuthentication(auth, 'commitment')
      const { id: eventId } = params

      if (!eventId) {
        throw new Error('Event ID is required')
      }

      const commitments = await this.commitmentService.getEventCommitments(eventId, userId)

      return response.ok(
        createSuccessResponse({
          data: commitments,
          message: 'Event commitments retrieved successfully',
        })
      )
    } catch (error) {
      return handleControllerError(error, response)
    }
  }
}
