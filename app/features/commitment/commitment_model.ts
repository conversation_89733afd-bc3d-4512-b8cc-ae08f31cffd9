import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import BaseModel from '#models/base_model'
import { column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import User from '#features/user/user_model'
import Task from '#features/task/task_model'
import Event from '#features/event/event_model'
import { EngagementStatus } from '#features/commitment/types/commitment_types'

/**
 * Commitment Model
 *
 * Represents invitations for tasks and events, allowing users to invite others
 * to collaborate on their activities. Uses nullable foreign keys pattern to
 * reference either tasks or events (never both).
 *
 * Based on the commitments table in the ERD.
 */
export default class Commitment extends compose(BaseModel, SoftDeletes) {
  static table = 'commitments'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare taskId: string | null

  @column()
  declare eventId: string | null

  @column()
  declare hostUserId: string

  @column()
  declare inviteeUserId: string

  @column()
  declare inviteeStatus: EngagementStatus

  @column()
  declare hostStatus: EngagementStatus

  @column()
  declare message: string | null

  @column()
  declare isAutoAccepted: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  /**
   * Relationship to the task (if this is a task commitment)
   */
  @belongsTo(() => Task, { foreignKey: 'taskId' })
  declare task: BelongsTo<typeof Task>

  /**
   * Relationship to the event (if this is an event commitment)
   */
  @belongsTo(() => Event, { foreignKey: 'eventId' })
  declare event: BelongsTo<typeof Event>

  /**
   * Relationship to the host user (who sent the invitation)
   */
  @belongsTo(() => User, { foreignKey: 'hostUserId' })
  declare hostUser: BelongsTo<typeof User>

  /**
   * Relationship to the invitee user (who received the invitation)
   */
  @belongsTo(() => User, { foreignKey: 'inviteeUserId' })
  declare inviteeUser: BelongsTo<typeof User>

  /**
   * Get the resource type ('task' or 'event')
   */
  get resourceType(): 'task' | 'event' {
    if (this.taskId) return 'task'
    if (this.eventId) return 'event'
    throw new Error('Commitment must have either taskId or eventId set')
  }

  /**
   * Get the resource ID (either taskId or eventId)
   */
  get resourceId(): string {
    if (this.taskId) return this.taskId
    if (this.eventId) return this.eventId
    throw new Error('Commitment must have either taskId or eventId set')
  }

  /**
   * Check if this is a task commitment
   */
  get isTaskCommitment(): boolean {
    return this.taskId !== null
  }

  /**
   * Check if this is an event commitment
   */
  get isEventCommitment(): boolean {
    return this.eventId !== null
  }

  /**
   * Check if the invitee status is pending
   */
  public isPending(): boolean {
    return this.inviteeStatus === EngagementStatus.PENDING
  }

  /**
   * Check if the invitee status is accepted
   */
  public isAccepted(): boolean {
    return this.inviteeStatus === EngagementStatus.ACCEPTED
  }

  /**
   * Check if the invitee status is declined
   */
  public isDeclined(): boolean {
    return this.inviteeStatus === EngagementStatus.DECLINED
  }

  /**
   * Check if the commitment can be responded to
   */
  public canBeRespondedTo(): boolean {
    return this.isPending()
  }

  /**
   * Check if a specific user can respond to this commitment
   */
  public canBeRespondedToBy(userId: string): boolean {
    if (!this.canBeRespondedTo()) return false
    return this.inviteeUserId === userId
  }

  /**
   * Check if a specific user can cancel this commitment
   */
  public canBeCancelledBy(userId: string): boolean {
    if (!this.isPending()) return false
    return this.hostUserId === userId
  }

  /**
   * Check if this commitment involves a specific user
   * (either as host or invitee)
   */
  public involvesUser(userId: string): boolean {
    return this.hostUserId === userId || this.inviteeUserId === userId
  }

  /**
   * Get a human-readable description of the commitment
   */
  public getDescription(): string {
    const resourceType = this.resourceType
    return `Invitation to collaborate on ${resourceType}`
  }

  /**
   * Get the title of the associated resource
   */
  public async getResourceTitle(): Promise<string> {
    if (this.isTaskCommitment) {
      if (this.task) {
        return this.task.title
      }
      const task = await Task.find(this.taskId!)
      return task?.title || 'Unknown Task'
    } else {
      if (this.event) {
        return this.event.title
      }
      const event = await Event.find(this.eventId!)
      return event?.title || 'Unknown Event'
    }
  }
}
