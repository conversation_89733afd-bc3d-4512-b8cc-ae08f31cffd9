import { test } from '@japa/runner'
import db from '@adonisjs/lucid/services/db'
import { createTestUserWithToken } from '#tests/utils/test-user-factory'

test.group('Commitment Collaboration', (group) => {
  let hostToken: string
  let inviteeToken: string
  let hostUserId: string
  let inviteeUserId: string
  let taskId: string
  let eventId: string
  let commitmentId: string

  // Helper function to create a test user (participates in database transactions)
  const createTestUser = async (prefix: string) => {
    const testUser = await createTestUserWithToken(prefix)
    return {
      token: testUser.token,
      userId: testUser.userId,
    }
  }

  group.setup(async () => {
    await db.beginGlobalTransaction()

    // Create a host user (task/event owner)
    const hostUser = await createTestUser('host')
    hostToken = hostUser.token
    hostUserId = hostUser.userId

    // Create invitee user
    const inviteeUser = await createTestUser('invitee')
    inviteeToken = inviteeUser.token
    inviteeUserId = inviteeUser.userId

    return () => db.rollbackGlobalTransaction()
  })

  test('host can create task for collaboration testing', async ({ assert }) => {
    if (!hostUserId) {
      assert.fail('Setup failed - no host user ID available')
      return
    }

    // Create task directly using the model to avoid AI endpoint issues
    const Task = (await import('#features/task/task_model')).default
    const { TaskStatus } = await import('#types/activity_types')

    const task = await Task.create({
      userId: hostUserId,
      title: 'Collaboration Test Task',
      description: 'Testing task collaboration features',
      status: TaskStatus.PENDING,
      entityType: 'user_save',
    })

    assert.exists(task.id)
    assert.isString(task.id)
    assert.equal(task.userId, hostUserId)

    taskId = task.id
  }).timeout(15000)

  test('host can create event for collaboration testing', async ({ client, assert }) => {
    if (!hostToken) {
      assert.fail('Setup failed - no host auth token available')
      return
    }

    const eventData = {
      title: 'Collaboration Test Event',
      description: 'Testing event collaboration features',
      startDate: '2024-12-02T14:00:00Z',
      endDate: '2024-12-02T16:00:00Z',
    }

    const response = await client.post('/api/v1/event').bearerToken(hostToken).json(eventData)

    response.assertStatus(201)
    const body = response.body()

    assert.exists(body.data)
    assert.exists(body.data.id)
    assert.isString(body.data.id)

    eventId = body.data.id
  }).timeout(15000)

  test('host can send task invitation', async ({ client, assert }) => {
    if (!hostToken || !taskId || !inviteeUserId) {
      assert.fail('Setup failed - missing required data')
      return
    }

    const invitationData = {
      inviteeUserId,
      message: 'Would you like to collaborate on this task?',
    }

    const response = await client
      .post(`/api/v1/tasks/${taskId}/invitations`)
      .bearerToken(hostToken)
      .json(invitationData)

    response.assertStatus(201)
    const body = response.body()

    assert.exists(body.data)
    assert.exists(body.data.id)
    assert.equal(body.data.taskId, taskId)
    assert.equal(body.data.hostUserId, hostUserId)
    assert.equal(body.data.inviteeUserId, inviteeUserId)
    assert.equal(body.data.inviteeStatus, 'pending')
    assert.equal(body.data.hostStatus, 'accepted')
    assert.equal(body.data.message, 'Would you like to collaborate on this task?')

    commitmentId = body.data.id
  }).timeout(15000)

  test('host can send event invitation', async ({ client, assert }) => {
    if (!hostToken || !eventId || !inviteeUserId) {
      assert.fail('Setup failed - missing required data')
      return
    }

    const invitationData = {
      inviteeUserId,
      message: 'Would you like to join this event?',
    }

    const response = await client
      .post(`/api/v1/events/${eventId}/invitations`)
      .bearerToken(hostToken)
      .json(invitationData)

    response.assertStatus(201)
    const body = response.body()

    assert.exists(body.data)
    assert.exists(body.data.id)
    assert.equal(body.data.eventId, eventId)
    assert.equal(body.data.hostUserId, hostUserId)
    assert.equal(body.data.inviteeUserId, inviteeUserId)
    assert.equal(body.data.inviteeStatus, 'pending')
    assert.equal(body.data.hostStatus, 'accepted')
    assert.equal(body.data.message, 'Would you like to join this event?')
  }).timeout(15000)

  test('invitee can view received invitations', async ({ client, assert }) => {
    if (!inviteeToken) {
      assert.fail('Setup failed - no invitee auth token available')
      return
    }

    const response = await client.get('/api/v1/invitations/received').bearerToken(inviteeToken)

    response.assertStatus(200)
    const body = response.body()

    assert.exists(body.data)
    assert.isArray(body.data)
    assert.isAtLeast(body.data.length, 2) // Should have task and event invitations

    // Find task invitation
    const taskInvitation = body.data.find((inv: any) => inv.resourceType === 'task')
    assert.exists(taskInvitation)
    assert.equal(taskInvitation.resourceId, taskId)
    assert.equal(taskInvitation.inviteeStatus, 'pending')

    // Find event invitation
    const eventInvitation = body.data.find((inv: any) => inv.resourceType === 'event')
    assert.exists(eventInvitation)
    assert.equal(eventInvitation.resourceId, eventId)
    assert.equal(eventInvitation.inviteeStatus, 'pending')
  }).timeout(15000)

  test('invitee can accept task invitation', async ({ client, assert }) => {
    if (!inviteeToken || !commitmentId) {
      assert.fail('Setup failed - missing required data')
      return
    }

    const responseData = {
      status: 'accepted',
    }

    const response = await client
      .put(`/api/v1/invitations/${commitmentId}`)
      .bearerToken(inviteeToken)
      .json(responseData)

    response.assertStatus(200)
    const body = response.body()

    assert.exists(body.data)
    assert.equal(body.data.id, commitmentId)
    assert.equal(body.data.inviteeStatus, 'accepted')
    assert.equal(body.message, 'Invitation accepted successfully')
  }).timeout(15000)

  test('host can view sent invitations', async ({ client, assert }) => {
    if (!hostToken) {
      assert.fail('Setup failed - no host auth token available')
      return
    }

    const response = await client.get('/api/v1/invitations/sent').bearerToken(hostToken)

    response.assertStatus(200)
    const body = response.body()

    assert.exists(body.data)
    assert.isArray(body.data)
    assert.isAtLeast(body.data.length, 2) // Should have task and event invitations

    // Find accepted task invitation
    const taskInvitation = body.data.find((inv: any) => inv.resourceType === 'task')
    assert.exists(taskInvitation)
    assert.equal(taskInvitation.inviteeStatus, 'accepted')
  }).timeout(15000)

  test('host can view task commitments', async ({ client, assert }) => {
    if (!hostToken || !taskId) {
      assert.fail('Setup failed - missing required data')
      return
    }

    const response = await client.get(`/api/v1/tasks/${taskId}/invitations`).bearerToken(hostToken)

    response.assertStatus(200)
    const body = response.body()

    assert.exists(body.data)
    assert.isArray(body.data)
    assert.isAtLeast(body.data.length, 1)

    const commitment = body.data[0]
    assert.equal(commitment.taskId, taskId)
    assert.equal(commitment.inviteeStatus, 'accepted')
  }).timeout(15000)

  test('host can cancel pending invitation', async ({ client, assert }) => {
    if (!hostToken || !hostUserId || !inviteeUserId) {
      assert.fail('Setup failed - missing required data')
      return
    }

    // Create a new task for this test to avoid duplicate invitation issues
    const Task = (await import('#features/task/task_model')).default
    const { TaskStatus } = await import('#types/activity_types')

    const newTask = await Task.create({
      userId: hostUserId,
      title: 'Cancel Test Task',
      description: 'Task for testing invitation cancellation',
      status: TaskStatus.PENDING,
      entityType: 'user_save',
    })

    // Create an invitation to cancel
    const invitationData = {
      inviteeUserId,
      message: 'Test invitation to cancel',
    }

    const createResponse = await client
      .post(`/api/v1/tasks/${newTask.id}/invitations`)
      .bearerToken(hostToken)
      .json(invitationData)

    createResponse.assertStatus(201)
    const newCommitmentId = createResponse.body().data.id

    // Now cancel it
    const cancelResponse = await client
      .delete(`/api/v1/invitations/${newCommitmentId}`)
      .bearerToken(hostToken)

    cancelResponse.assertStatus(200)
    const body = cancelResponse.body()

    assert.exists(body.data)
    assert.equal(body.message, 'Invitation cancelled successfully')
  }).timeout(15000)

  test('invitee cannot respond to cancelled invitation', async ({ client, assert }) => {
    if (!inviteeToken || !hostToken || !hostUserId || !inviteeUserId) {
      assert.fail('Setup failed - missing required data')
      return
    }

    // Create a new task and invitation for this test
    const Task = (await import('#features/task/task_model')).default
    const { TaskStatus } = await import('#types/activity_types')

    const testTask = await Task.create({
      userId: hostUserId,
      title: 'Cancelled Invitation Test Task',
      description: 'Task for testing cancelled invitation response',
      status: TaskStatus.PENDING,
      entityType: 'user_save',
    })

    // Create an invitation
    const invitationData = {
      inviteeUserId,
      message: 'Test invitation to cancel and respond to',
    }

    const createResponse = await client
      .post(`/api/v1/tasks/${testTask.id}/invitations`)
      .bearerToken(hostToken)
      .json(invitationData)

    createResponse.assertStatus(201)
    const invitationId = createResponse.body().data.id

    // Cancel the invitation
    const cancelResponse = await client
      .delete(`/api/v1/invitations/${invitationId}`)
      .bearerToken(hostToken)

    cancelResponse.assertStatus(200)

    // Try to respond to the cancelled invitation
    const responseData = {
      status: 'accepted',
    }

    // This should fail since the invitation was cancelled (soft deleted)
    const response = await client
      .put(`/api/v1/invitations/${invitationId}`)
      .bearerToken(inviteeToken)
      .json(responseData)

    // Cancelled invitations should return 404 (not found due to soft delete)
    response.assertStatus(404)
  }).timeout(15000)

  test('unauthorized user cannot send invitations', async ({ client, assert }) => {
    if (!taskId || !inviteeUserId) {
      assert.fail('Setup failed - missing required data')
      return
    }

    const invitationData = {
      inviteeUserId,
      message: 'Unauthorized invitation',
    }

    const response = await client
      .post(`/api/v1/tasks/${taskId}/invitations`)
      .bearerToken(inviteeToken) // Using invitee token (not task owner)
      .json(invitationData)

    // The system returns 403 for authorization errors when user doesn't own the resource
    response.assertStatus(403)
  }).timeout(15000)
})
