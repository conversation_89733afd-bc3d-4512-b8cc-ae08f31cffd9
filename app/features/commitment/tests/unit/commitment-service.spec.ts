import { test } from '@japa/runner'
import db from '@adonisjs/lucid/services/db'
import CommitmentService from '#features/commitment/commitment_service'
import { createTestUserWithToken } from '#tests/utils/test-user-factory'
import Task from '#features/task/task_model'
import Event from '#features/event/event_model'
import { EngagementStatus } from '#features/commitment/types/commitment_types'
import { TaskStatus, TaskEntityType, EventStatus } from '#types/activity_types'

test.group('CommitmentService', (group) => {
  let commitmentService: CommitmentService
  let hostUserId: string
  let inviteeUserId: string
  let taskId: string
  let eventId: string

  group.setup(async () => {
    await db.beginGlobalTransaction()

    commitmentService = new CommitmentService()

    // Create test users
    const hostUser = await createTestUserWithToken('host')
    const inviteeUser = await createTestUserWithToken('invitee')

    hostUserId = hostUser.userId
    inviteeUserId = inviteeUser.userId

    // Create test task
    const task = await Task.create({
      userId: hostUserId,
      title: 'Test Task',
      description: 'Task for testing commitments',
      status: TaskStatus.PENDING,
      entityType: TaskEntityType.USER_SAVE,
    })
    taskId = task.id

    // Create test event
    const event = await Event.create({
      userId: hostUserId,
      title: 'Test Event',
      description: 'Event for testing commitments',
      status: EventStatus.ACTIVE,
    })
    eventId = event.id

    return () => db.rollbackGlobalTransaction()
  })

  test('can create task invitation', async ({ assert }) => {
    const data = {
      inviteeUserId,
      message: 'Would you like to collaborate?',
    }

    const commitment = await commitmentService.createTaskInvitation(taskId, hostUserId, data)

    assert.exists(commitment)
    assert.equal(commitment.taskId, taskId)
    assert.equal(commitment.hostUserId, hostUserId)
    assert.equal(commitment.inviteeUserId, inviteeUserId)
    assert.equal(commitment.inviteeStatus, EngagementStatus.PENDING)
    assert.equal(commitment.hostStatus, EngagementStatus.ACCEPTED)
    assert.equal(commitment.message, 'Would you like to collaborate?')
    assert.isFalse(commitment.isAutoAccepted)
  })

  test('can respond to commitment invitation', async ({ assert }) => {
    // Create a separate task for this test
    const task2 = await Task.create({
      userId: hostUserId,
      title: 'Test Task 2',
      description: 'Another task for testing commitments',
      status: TaskStatus.PENDING,
      entityType: TaskEntityType.USER_SAVE,
    })

    const data = {
      inviteeUserId,
      message: 'Test invitation',
    }

    const commitment = await commitmentService.createTaskInvitation(task2.id, hostUserId, data)

    const responseData = {
      status: EngagementStatus.ACCEPTED as EngagementStatus.ACCEPTED,
    }

    const updatedCommitment = await commitmentService.respondToCommitment(
      commitment.id,
      inviteeUserId,
      responseData
    )

    assert.equal(updatedCommitment.id, commitment.id)
    assert.equal(updatedCommitment.inviteeStatus, EngagementStatus.ACCEPTED)
  })
})
