import vine from '@vinejs/vine'
import {
  EngagementStatus,
  ENGAGEMENT_STATUS_VALUES,
} from '#features/commitment/types/commitment_types'

/**
 * Validator for creating a commitment invitation
 */
export const createCommitmentValidator = vine.compile(
  vine.object({
    inviteeUserId: vine.string().uuid(),
    message: vine.string().trim().maxLength(500).optional(),
  })
)

/**
 * Validator for responding to commitment invitations
 */
export const respondToCommitmentValidator = vine.compile(
  vine.object({
    status: vine.enum([EngagementStatus.ACCEPTED, EngagementStatus.DECLINED]),
  })
)

/**
 * Validator for commitment query parameters
 */
export const commitmentQueryValidator = vine.compile(
  vine.object({
    // Filters
    status: vine.enum(ENGAGEMENT_STATUS_VALUES).optional(),
    resourceType: vine.enum(['task', 'event']).optional(),
    search: vine.string().trim().optional(),

    // Sorting
    sortBy: vine.enum(['createdAt', 'updatedAt', 'inviteeStatus']).optional(),
    sortDirection: vine.enum(['asc', 'desc']).optional(),

    // Pagination
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
  })
)
