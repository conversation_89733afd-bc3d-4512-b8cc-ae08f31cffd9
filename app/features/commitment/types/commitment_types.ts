export enum EngagementStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
}

export const ENGAGEMENT_STATUS_VALUES = Object.values(EngagementStatus)

export interface CreateCommitmentData {
  inviteeUserId: string
  message?: string
}

export interface RespondToCommitmentData {
  status: EngagementStatus.ACCEPTED | EngagementStatus.DECLINED
}

export interface CommitmentListResponse {
  id: string
  resourceType: 'task' | 'event'
  resourceId: string
  resourceTitle: string
  hostUserId: string
  inviteeUserId: string
  inviteeStatus: EngagementStatus
  hostStatus: EngagementStatus
  message?: string
  isAutoAccepted: boolean
  createdAt: string
  updatedAt: string
}
