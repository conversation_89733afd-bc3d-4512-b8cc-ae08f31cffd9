import { inject } from '@adonisjs/core'
import Commitment from './commitment_model.js'
import Task from '#features/task/task_model'
import Event from '#features/event/event_model'
import User from '#features/user/user_model'
import {
  CreateCommitmentData,
  RespondToCommitmentData,
  EngagementStatus,
  CommitmentListResponse,
} from '#features/commitment/types/commitment_types'
import { authorizationException, notFoundException, validationException } from '#utils/error'

/**
 * CommitmentService
 *
 * Handles business logic for commitment management:
 * - Creating invitations for tasks and events
 * - Responding to invitations
 * - Auto-acceptance based on profile settings
 * - Permission checks for commitment operations
 */
@inject()
export default class CommitmentService {
  /**
   * Create a task invitation
   */
  async createTaskInvitation(
    taskId: string,
    hostUserId: string,
    data: CreateCommitmentData
  ): Promise<Commitment> {
    // Verify task exists
    const task = await Task.find(taskId)
    if (!task) {
      throw notFoundException('Task not found')
    }

    // Check if host owns the task
    if (task.userId !== hostUserId) {
      throw authorizationException('Only the task owner can send invitations')
    }

    // Check if invitee user exists
    const inviteeUser = await User.find(data.inviteeUserId)
    if (!inviteeUser) {
      throw notFoundException('Invitee user not found')
    }

    // Prevent self-invitation
    if (hostUserId === data.inviteeUserId) {
      throw validationException('Cannot invite yourself')
    }

    // Check for existing pending invitation
    const existingCommitment = await Commitment.query()
      .where('taskId', taskId)
      .where('hostUserId', hostUserId)
      .where('inviteeUserId', data.inviteeUserId)
      .where('inviteeStatus', EngagementStatus.PENDING)
      .first()

    if (existingCommitment) {
      throw validationException('User already has a pending invitation for this task')
    }

    // Check auto-acceptance setting
    let autoAccept = false
    try {
      await inviteeUser.load('profile', (profileQuery) => {
        profileQuery.preload('settings')
      })
      autoAccept = inviteeUser.profile?.settings?.defaultAutoAcceptInvitations || false
    } catch {
      // Default to false if profile or settings don't exist
      autoAccept = false
    }

    // Create the commitment
    return Commitment.create({
      taskId,
      eventId: null,
      hostUserId,
      inviteeUserId: data.inviteeUserId,
      inviteeStatus: autoAccept ? EngagementStatus.ACCEPTED : EngagementStatus.PENDING,
      hostStatus: EngagementStatus.ACCEPTED,
      message: data.message || null,
      isAutoAccepted: autoAccept,
    })
  }

  /**
   * Create an event invitation
   */
  async createEventInvitation(
    eventId: string,
    hostUserId: string,
    data: CreateCommitmentData
  ): Promise<Commitment> {
    // Verify event exists
    const event = await Event.find(eventId)
    if (!event) {
      throw notFoundException('Event not found')
    }

    // Check if host owns the event
    if (event.userId !== hostUserId) {
      throw authorizationException('Only the event owner can send invitations')
    }

    // Check if invitee user exists
    const inviteeUser = await User.find(data.inviteeUserId)
    if (!inviteeUser) {
      throw notFoundException('Invitee user not found')
    }

    // Prevent self-invitation
    if (hostUserId === data.inviteeUserId) {
      throw validationException('Cannot invite yourself')
    }

    // Check for existing pending invitation
    const existingCommitment = await Commitment.query()
      .where('eventId', eventId)
      .where('hostUserId', hostUserId)
      .where('inviteeUserId', data.inviteeUserId)
      .where('inviteeStatus', EngagementStatus.PENDING)
      .first()

    if (existingCommitment) {
      throw validationException('User already has a pending invitation for this event')
    }

    // Check auto-acceptance setting
    let autoAccept = false
    try {
      await inviteeUser.load('profile', (profileQuery) => {
        profileQuery.preload('settings')
      })
      autoAccept = inviteeUser.profile?.settings?.defaultAutoAcceptInvitations || false
    } catch {
      // Default to false if profile or settings don't exist
      autoAccept = false
    }

    // Create the commitment
    return Commitment.create({
      taskId: null,
      eventId,
      hostUserId,
      inviteeUserId: data.inviteeUserId,
      inviteeStatus: autoAccept ? EngagementStatus.ACCEPTED : EngagementStatus.PENDING,
      hostStatus: EngagementStatus.ACCEPTED,
      message: data.message || null,
      isAutoAccepted: autoAccept,
    })
  }

  /**
   * Respond to a commitment invitation
   */
  async respondToCommitment(
    commitmentId: string,
    respondingUserId: string,
    data: RespondToCommitmentData
  ): Promise<Commitment> {
    const commitment = await Commitment.find(commitmentId)
    if (!commitment) {
      throw notFoundException('Commitment not found')
    }

    if (!commitment.canBeRespondedTo()) {
      throw validationException('This invitation cannot be responded to')
    }

    if (!commitment.canBeRespondedToBy(respondingUserId)) {
      throw authorizationException('Only the invited user can respond to this invitation')
    }

    // Update the status
    commitment.inviteeStatus = data.status
    await commitment.save()

    return commitment
  }

  /**
   * Cancel a commitment invitation (host only)
   */
  async cancelCommitment(commitmentId: string, hostUserId: string): Promise<Commitment> {
    const commitment = await Commitment.find(commitmentId)
    if (!commitment) {
      throw notFoundException('Commitment not found')
    }

    if (!commitment.canBeCancelledBy(hostUserId)) {
      throw authorizationException('Only the host can cancel this invitation')
    }

    // Soft delete the commitment
    await commitment.delete()
    return commitment
  }

  /**
   * Get received invitations for a user (unified view of tasks and events)
   */
  async getReceivedInvitations(userId: string): Promise<CommitmentListResponse[]> {
    const commitments = await Commitment.query()
      .where('inviteeUserId', userId)
      .preload('task')
      .preload('event')
      .preload('hostUser')
      .orderBy('createdAt', 'desc')

    const responses: CommitmentListResponse[] = []

    for (const commitment of commitments) {
      let resourceTitle = 'Unknown'

      if (commitment.isTaskCommitment && commitment.task) {
        resourceTitle = commitment.task.title
      } else if (commitment.isEventCommitment && commitment.event) {
        resourceTitle = commitment.event.title
      }

      responses.push({
        id: commitment.id,
        resourceType: commitment.resourceType,
        resourceId: commitment.resourceId,
        resourceTitle,
        hostUserId: commitment.hostUserId,
        inviteeUserId: commitment.inviteeUserId,
        inviteeStatus: commitment.inviteeStatus,
        hostStatus: commitment.hostStatus,
        message: commitment.message || undefined,
        isAutoAccepted: commitment.isAutoAccepted,
        createdAt: commitment.createdAt.toISO()!,
        updatedAt: commitment.updatedAt.toISO()!,
      })
    }

    return responses
  }

  /**
   * Get sent invitations for a user (unified view of tasks and events)
   */
  async getSentInvitations(userId: string): Promise<CommitmentListResponse[]> {
    const commitments = await Commitment.query()
      .where('hostUserId', userId)
      .preload('task')
      .preload('event')
      .preload('inviteeUser')
      .orderBy('createdAt', 'desc')

    const responses: CommitmentListResponse[] = []

    for (const commitment of commitments) {
      let resourceTitle = 'Unknown'

      if (commitment.isTaskCommitment && commitment.task) {
        resourceTitle = commitment.task.title
      } else if (commitment.isEventCommitment && commitment.event) {
        resourceTitle = commitment.event.title
      }

      responses.push({
        id: commitment.id,
        resourceType: commitment.resourceType,
        resourceId: commitment.resourceId,
        resourceTitle,
        hostUserId: commitment.hostUserId,
        inviteeUserId: commitment.inviteeUserId,
        inviteeStatus: commitment.inviteeStatus,
        hostStatus: commitment.hostStatus,
        message: commitment.message || undefined,
        isAutoAccepted: commitment.isAutoAccepted,
        createdAt: commitment.createdAt.toISO()!,
        updatedAt: commitment.updatedAt.toISO()!,
      })
    }

    return responses
  }

  /**
   * Get commitments for a specific task
   */
  async getTaskCommitments(taskId: string, userId: string): Promise<Commitment[]> {
    // Verify task exists and user has access
    const task = await Task.find(taskId)
    if (!task) {
      throw notFoundException('Task not found')
    }

    if (task.userId !== userId) {
      throw authorizationException('Access denied')
    }

    return Commitment.query()
      .where('taskId', taskId)
      .preload('inviteeUser')
      .orderBy('createdAt', 'desc')
  }

  /**
   * Get commitments for a specific event
   */
  async getEventCommitments(eventId: string, userId: string): Promise<Commitment[]> {
    // Verify event exists and user has access
    const event = await Event.find(eventId)
    if (!event) {
      throw notFoundException('Event not found')
    }

    if (event.userId !== userId) {
      throw authorizationException('Access denied')
    }

    return Commitment.query()
      .where('eventId', eventId)
      .preload('inviteeUser')
      .orderBy('createdAt', 'desc')
  }
}
