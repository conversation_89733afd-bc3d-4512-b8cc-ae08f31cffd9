import BaseModel from '#models/base_model'
import { column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import { EventStatus } from '#types/activity_types'
import User from '#features/user/user_model'
import PrivacyAssignment from '#models/privacy_assignment'
import Commitment from '#features/commitment/commitment_model'

export default class Event extends compose(BaseModel, SoftDeletes) {
  @column({ isPrimary: true })
  declare id: string

  @column()
  declare userId: string

  @belongsTo(() => User, { foreignKey: 'userId' })
  declare user: BelongsTo<typeof User>

  // Activity fields now included directly in Event
  @column()
  declare title: string

  @column()
  declare description: string | null

  @column.dateTime({ columnName: 'start_date' })
  declare startDate: DateTime | null

  @column.dateTime({ columnName: 'end_date' })
  declare endDate: DateTime | null

  @column({
    prepare: (value: any) => (typeof value === 'object' ? JSON.stringify(value) : value),
    consume: (value: string) => {
      if (!value) return null
      if (typeof value === 'string') {
        try {
          return JSON.parse(value)
        } catch {
          return value
        }
      }
      return value
    },
  })
  declare location: Record<string, any> | null

  // Event-specific fields
  @column()
  declare status: EventStatus

  @column()
  declare maxAttendees: number | null

  // Standard timestamps and soft delete support
  @hasMany(() => PrivacyAssignment, {
    foreignKey: 'eventId',
    onQuery: (query) => query.whereNotNull('event_id'),
  })
  declare privacyAssignments: HasMany<typeof PrivacyAssignment>

  @hasMany(() => Commitment, {
    foreignKey: 'eventId',
    onQuery: (query) => query.whereNotNull('event_id'),
  })
  declare commitments: HasMany<typeof Commitment>

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  declare updatedAt: DateTime

  @column.dateTime({ columnName: 'deleted_at' })
  declare deletedAt: DateTime | null
}
