import { test } from '@japa/runner'
import { ApiClient } from '@japa/api-client'
import db from '@adonisjs/lucid/services/db'
import { createTestUserWithToken } from '#tests/utils/test-user-factory'

test.group('Profile Settings Basic', (group) => {
  group.setup(async () => {
    await db.beginGlobalTransaction()
    return () => db.rollbackGlobalTransaction()
  })

  // Helper function to create a test user (participates in database transactions)
  async function createTestUser(prefix: string = 'user') {
    const testUser = await createTestUserWithToken(prefix)
    return {
      authToken: testUser.token,
      userId: testUser.userId,
    }
  }

  // Helper function to create user's profile (required for profile settings)
  async function createUserProfile(client: ApiClient, authToken: string) {
    const profileData = {
      firstName: 'Test',
      lastName: 'User',
      birthDate: '1990-01-01',
      countryCode: 'PHL',
    }

    const response = await client.post('/api/v1/profile').bearerToken(authToken).json(profileData)

    if (response.response.status !== 201) {
      throw new Error('Failed to create user profile')
    }

    return response.body().data
  }

  // Helper function to create a privacy policy template
  async function createPrivacyTemplate(client: ApiClient, authToken: string, templateData = {}) {
    const defaultTemplateData = {
      name: 'Test Privacy Template',
      description: 'Test template for profile settings',
      ...templateData,
    }

    const response = await client
      .post('/api/v1/privacy-profiles')
      .bearerToken(authToken)
      .json(defaultTemplateData)

    if (response.response.status !== 201) {
      throw new Error('Failed to create privacy template')
    }

    return response.body().data
  }

  // Helper function to create profile settings for a profile
  async function createProfileSettings(
    client: ApiClient,
    authToken: string,
    profileId: string,
    settingsData = {}
  ) {
    const defaultSettingsData = {
      profileId,
      defaultAutoAcceptInvitations: false,
      globalBusyMessage: 'Currently busy',
      ...settingsData,
    }

    const response = await client
      .post('/api/v1/profile-settings')
      .bearerToken(authToken)
      .json(defaultSettingsData)

    return { response, settingsData: defaultSettingsData }
  }

  test('user can create profile settings', async ({ client, assert }) => {
    const { authToken } = await createTestUser('settingsuser')

    // Create user's profile
    const profile = await createUserProfile(client, authToken)

    // Create profile settings
    const { response, settingsData } = await createProfileSettings(client, authToken, profile.id, {
      defaultAutoAcceptInvitations: true,
      globalBusyMessage: 'In a meeting',
    })

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.profileId, profile.id)
    assert.equal(body.data.defaultAutoAcceptInvitations, true)
    assert.equal(body.data.globalBusyMessage, 'In a meeting')
    assert.exists(body.data.id)
    assert.exists(body.data.createdAt)
    assert.exists(body.data.updatedAt)
  }).timeout(10000)

  test('user can create profile settings with privacy policy template', async ({
    client,
    assert,
  }) => {
    const { authToken } = await createTestUser('settingstemplate')

    // Get user's profile first
    const profile = await createUserProfile(client, authToken)

    // Create a privacy policy template first
    const privacyTemplate = await createPrivacyTemplate(client, authToken, { name: 'Work Privacy' })

    const settingsData = {
      profileId: profile.id,
      defaultAutoAcceptInvitations: false,
      globalBusyMessage: 'Working',
      defaultPolicyTemplateId: privacyTemplate.id,
    }

    const response = await client
      .post('/api/v1/profile-settings')
      .bearerToken(authToken)
      .json(settingsData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.data.profileId, settingsData.profileId)
    assert.equal(body.data.defaultPolicyTemplateId, privacyTemplate.id)
    assert.equal(body.data.globalBusyMessage, settingsData.globalBusyMessage)
  }).timeout(10000)

  test('user can create minimal profile settings with only profileId', async ({
    client,
    assert,
  }) => {
    const { authToken } = await createTestUser('minimalsettings')

    // Get user's profile first
    const profile = await createUserProfile(client, authToken)

    const settingsData = {
      profileId: profile.id,
    }

    const response = await client
      .post('/api/v1/profile-settings')
      .bearerToken(authToken)
      .json(settingsData)

    response.assertStatus(201)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.data.profileId, settingsData.profileId)
    assert.equal(body.data.defaultAutoAcceptInvitations, false) // Default value
    assert.isNull(body.data.globalBusyMessage)
    assert.isNull(body.data.defaultPolicyTemplateId)
  }).timeout(10000)

  test('profile settings creation validates required fields', async ({ client, assert }) => {
    const { authToken } = await createTestUser('validationsettings')

    // Test missing profileId
    const response1 = await client.post('/api/v1/profile-settings').bearerToken(authToken).json({})

    response1.assertStatus(422) // Validation error

    // Test invalid profileId format
    const response2 = await client.post('/api/v1/profile-settings').bearerToken(authToken).json({
      profileId: 'invalid-uuid',
    })

    response2.assertStatus(422)

    // Test globalBusyMessage too long
    const profile = await createUserProfile(client, authToken)
    const response3 = await client
      .post('/api/v1/profile-settings')
      .bearerToken(authToken)
      .json({
        profileId: profile.id,
        globalBusyMessage: 'a'.repeat(256), // Exceeds 255 character limit
      })

    response3.assertStatus(422)

    // Test invalid defaultPolicyTemplateId format
    const response4 = await client.post('/api/v1/profile-settings').bearerToken(authToken).json({
      profileId: profile.id,
      defaultPolicyTemplateId: 'invalid-uuid',
    })

    response4.assertStatus(422)
  }).timeout(10000)

  test('user cannot create duplicate profile settings for same profile', async ({
    client,
    assert,
  }) => {
    const { authToken } = await createTestUser('duplicatesettings')

    // Get user's profile first
    const profile = await createUserProfile(client, authToken)

    // Create first profile settings
    const { response: firstResponse } = await createProfileSettings(client, authToken, profile.id)
    firstResponse.assertStatus(201)

    // Try to create duplicate settings for same profile
    const response = await client.post('/api/v1/profile-settings').bearerToken(authToken).json({
      profileId: profile.id,
      defaultAutoAcceptInvitations: true,
    })

    response.assertStatus(422) // Should fail due to uniqueness constraint
  }).timeout(10000)

  test('user cannot create settings for other users profiles', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('settingsuser1')
    const { authToken: user2Token } = await createTestUser('settingsuser2')

    // Get user1's profile
    const user1Profile = await createUserProfile(client, user1Token)

    // User 2 tries to create settings for User 1's profile
    const response = await client.post('/api/v1/profile-settings').bearerToken(user2Token).json({
      profileId: user1Profile.id,
      defaultAutoAcceptInvitations: true,
    })

    response.assertStatus(404) // Not found (due to ownership check design in Profile Settings controller)
  }).timeout(10000)

  test('user can get specific profile settings by ID', async ({ client, assert }) => {
    const { authToken } = await createTestUser('getsettings')

    // Get user's profile first
    const profile = await createUserProfile(client, authToken)

    // Create profile settings first
    const { response: createResponse } = await createProfileSettings(client, authToken, profile.id)
    createResponse.assertStatus(201)
    const settingsId = createResponse.body().data.id

    // Get the profile settings
    const response = await client
      .get(`/api/v1/profile-settings/${settingsId}`)
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.data)
    assert.equal(body.data.id, settingsId)
    assert.equal(body.data.profileId, profile.id)
    assert.equal(body.data.globalBusyMessage, 'Currently busy')
  }).timeout(10000)

  test('user cannot access other users profile settings', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('getsettingsuser1')
    const { authToken: user2Token } = await createTestUser('getsettingsuser2')

    // User 1 creates profile settings
    const user1Profile = await createUserProfile(client, user1Token)
    const { response: createResponse } = await createProfileSettings(
      client,
      user1Token,
      user1Profile.id
    )
    createResponse.assertStatus(201)
    const settingsId = createResponse.body().data.id

    // User 2 tries to access User 1's profile settings
    const response = await client
      .get(`/api/v1/profile-settings/${settingsId}`)
      .bearerToken(user2Token)

    response.assertStatus(403) // Forbidden (due to ownership check)
  }).timeout(10000)

  test('user can update profile settings', async ({ client, assert }) => {
    const { authToken } = await createTestUser('updatesettings')

    // Get user's profile first
    const profile = await createUserProfile(client, authToken)

    // Create profile settings first
    const { response: createResponse } = await createProfileSettings(client, authToken, profile.id)
    createResponse.assertStatus(201)
    const settingsId = createResponse.body().data.id

    // Update the profile settings
    const updateData = {
      defaultAutoAcceptInvitations: true,
      globalBusyMessage: 'Updated busy message',
    }

    const response = await client
      .put(`/api/v1/profile-settings/${settingsId}`)
      .bearerToken(authToken)
      .json(updateData)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.equal(body.data.defaultAutoAcceptInvitations, updateData.defaultAutoAcceptInvitations)
    assert.equal(body.data.globalBusyMessage, updateData.globalBusyMessage)
    assert.equal(body.data.id, settingsId)
    assert.equal(body.data.profileId, profile.id) // Should remain unchanged
  }).timeout(10000)

  test('user can partially update profile settings', async ({ client, assert }) => {
    const { authToken } = await createTestUser('partialupdatesettings')

    // Get user's profile first
    const profile = await createUserProfile(client, authToken)

    // Create profile settings first
    const { response: createResponse, settingsData } = await createProfileSettings(
      client,
      authToken,
      profile.id
    )
    createResponse.assertStatus(201)
    const settingsId = createResponse.body().data.id

    // Partial update - only globalBusyMessage
    const updateData = {
      globalBusyMessage: 'Partially updated message',
    }

    const response = await client
      .put(`/api/v1/profile-settings/${settingsId}`)
      .bearerToken(authToken)
      .json(updateData)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.data.globalBusyMessage, updateData.globalBusyMessage)
    // Other fields should remain unchanged
    assert.equal(body.data.defaultAutoAcceptInvitations, settingsData.defaultAutoAcceptInvitations)
  }).timeout(10000)

  test('user can update privacy policy template reference', async ({ client, assert }) => {
    const { authToken } = await createTestUser('updatesettingstemplate')

    // Get user's profile first
    const profile = await createUserProfile(client, authToken)

    // Create profile settings first
    const { response: createResponse } = await createProfileSettings(client, authToken, profile.id)
    createResponse.assertStatus(201)
    const settingsId = createResponse.body().data.id

    // Create a privacy policy template
    const privacyTemplate = await createPrivacyTemplate(client, authToken, {
      name: 'Updated Privacy',
    })

    // Update to reference the privacy template
    const updateData = {
      defaultPolicyTemplateId: privacyTemplate.id,
    }

    const response = await client
      .put(`/api/v1/profile-settings/${settingsId}`)
      .bearerToken(authToken)
      .json(updateData)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.data.defaultPolicyTemplateId, privacyTemplate.id)
  }).timeout(10000)

  test('user cannot update other users profile settings', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('updatesettingsuser1')
    const { authToken: user2Token } = await createTestUser('updatesettingsuser2')

    // User 1 creates profile settings
    const user1Profile = await createUserProfile(client, user1Token)
    const { response: createResponse } = await createProfileSettings(
      client,
      user1Token,
      user1Profile.id
    )
    createResponse.assertStatus(201)
    const settingsId = createResponse.body().data.id

    // User 2 tries to update User 1's profile settings
    const response = await client
      .put(`/api/v1/profile-settings/${settingsId}`)
      .bearerToken(user2Token)
      .json({ globalBusyMessage: 'Hacked message' })

    response.assertStatus(403) // Forbidden (due to ownership check)
  }).timeout(10000)

  test('user can delete profile settings', async ({ client, assert }) => {
    const { authToken } = await createTestUser('deletesettings')

    // Get user's profile first
    const profile = await createUserProfile(client, authToken)

    // Create profile settings first
    const { response: createResponse } = await createProfileSettings(client, authToken, profile.id)
    createResponse.assertStatus(201)
    const settingsId = createResponse.body().data.id

    // Delete the profile settings
    const response = await client
      .delete(`/api/v1/profile-settings/${settingsId}`)
      .bearerToken(authToken)

    response.assertStatus(200)
    const body = response.body()

    assert.equal(body.status, 'success')
    assert.exists(body.message)

    // Verify profile settings are deleted (should return 404)
    const getResponse = await client
      .get(`/api/v1/profile-settings/${settingsId}`)
      .bearerToken(authToken)

    getResponse.assertStatus(404)
  }).timeout(10000)

  test('user cannot delete other users profile settings', async ({ client, assert }) => {
    const { authToken: user1Token } = await createTestUser('deletesettingsuser1')
    const { authToken: user2Token } = await createTestUser('deletesettingsuser2')

    // User 1 creates profile settings
    const user1Profile = await createUserProfile(client, user1Token)
    const { response: createResponse } = await createProfileSettings(
      client,
      user1Token,
      user1Profile.id
    )
    createResponse.assertStatus(201)
    const settingsId = createResponse.body().data.id

    // User 2 tries to delete User 1's profile settings
    const response = await client
      .delete(`/api/v1/profile-settings/${settingsId}`)
      .bearerToken(user2Token)

    response.assertStatus(403) // Forbidden (due to ownership check)
  }).timeout(10000)

  test('profile settings operations require authentication', async ({ client, assert }) => {
    // Test all endpoints without authentication
    const response1 = await client.post('/api/v1/profile-settings').json({
      profileId: 'test-id',
    })
    response1.assertStatus(401)

    const response2 = await client.get('/api/v1/profile-settings/123')
    response2.assertStatus(401)

    const response3 = await client.put('/api/v1/profile-settings/123').json({
      globalBusyMessage: 'Test',
    })
    response3.assertStatus(401)

    const response4 = await client.delete('/api/v1/profile-settings/123')
    response4.assertStatus(401)
  }).timeout(10000)

  test('profile settings enforce one-to-one relationship with profile', async ({
    client,
    assert,
  }) => {
    const { authToken } = await createTestUser('onetoone')

    // Get user's profile first
    const profile = await createUserProfile(client, authToken)

    // Create first profile settings
    const { response: firstResponse } = await createProfileSettings(client, authToken, profile.id, {
      globalBusyMessage: 'First settings',
    })
    firstResponse.assertStatus(201)

    // Attempt to create second settings for same profile should fail
    const response = await client.post('/api/v1/profile-settings').bearerToken(authToken).json({
      profileId: profile.id,
      globalBusyMessage: 'Second settings',
    })

    // Should fail due to one-to-one constraint
    response.assertStatus(422)
  }).timeout(10000)

  test('profile settings with privacy template integration', async ({ client, assert }) => {
    const { authToken } = await createTestUser('templateintegration')

    // Get user's profile first
    const profile = await createUserProfile(client, authToken)

    // Create multiple privacy templates
    const workTemplate = await createPrivacyTemplate(client, authToken, { name: 'Work Privacy' })
    const personalTemplate = await createPrivacyTemplate(client, authToken, {
      name: 'Personal Privacy',
    })

    // Create profile settings with work template
    const { response: createResponse } = await createProfileSettings(
      client,
      authToken,
      profile.id,
      {
        defaultPolicyTemplateId: workTemplate.id,
        globalBusyMessage: 'Using work privacy',
      }
    )
    createResponse.assertStatus(201)
    const settingsId = createResponse.body().data.id

    // Verify initial template reference
    let getResponse = await client
      .get(`/api/v1/profile-settings/${settingsId}`)
      .bearerToken(authToken)

    getResponse.assertStatus(200)
    assert.equal(getResponse.body().data.defaultPolicyTemplateId, workTemplate.id)

    // Update to use personal template
    const updateResponse = await client
      .put(`/api/v1/profile-settings/${settingsId}`)
      .bearerToken(authToken)
      .json({
        defaultPolicyTemplateId: personalTemplate.id,
        globalBusyMessage: 'Using personal privacy',
      })

    updateResponse.assertStatus(200)
    assert.equal(updateResponse.body().data.defaultPolicyTemplateId, personalTemplate.id)
    assert.equal(updateResponse.body().data.globalBusyMessage, 'Using personal privacy')

    // Verify updated template reference
    getResponse = await client.get(`/api/v1/profile-settings/${settingsId}`).bearerToken(authToken)

    getResponse.assertStatus(200)
    assert.equal(getResponse.body().data.defaultPolicyTemplateId, personalTemplate.id)
  }).timeout(15000)

  test('profile settings cannot reference other users privacy templates', async ({
    client,
    assert,
  }) => {
    const { authToken: user1Token } = await createTestUser('templateuser1')
    const { authToken: user2Token } = await createTestUser('templateuser2')

    // User 1 creates a privacy template
    const user1Template = await createPrivacyTemplate(client, user1Token, {
      name: 'User 1 Template',
    })

    // User 2 gets their profile
    const user2Profile = await createUserProfile(client, user2Token)

    // User 2 tries to create settings referencing User 1's template
    const response = await client.post('/api/v1/profile-settings').bearerToken(user2Token).json({
      profileId: user2Profile.id,
      defaultPolicyTemplateId: user1Template.id, // Other user's template
      globalBusyMessage: 'Trying to use other template',
    })

    // This should fail at the business logic level (template ownership validation)
    // The exact response code may vary based on implementation
    assert.oneOf(response.response.status, [400, 404, 422])
  }).timeout(10000)
})
