import Profile from './profile_model.js'
import User from '#features/user/user_model'
import { DateTime } from 'luxon'
import type { ModelAttributes } from '@adonisjs/lucid/types/model'
import type { NonPatchableModelFields } from '#types/model_utilities'

// Derive patchable attributes from the Profile model
// These are attributes that can be part of a patch operation.
type AllowedProfilePatchAttributes = Omit<ModelAttributes<Profile>, NonPatchableModelFields>

// ProfileForMerge: Represents the data structure (all fields optional)
// ready to be merged into the Lucid model instance.
// birthDate here will be `DateTime | null` as derived from the model.
type ProfileForMerge = Partial<AllowedProfilePatchAttributes>

// ProfilePatchData: Represents the data structure for API input.
// It's mostly like ProfileForMerge, but birthDate is specifically typed as `string | null`.
type ProfilePatchData = Omit<ProfileForMerge, 'birthDate'> & {
  birthDate?: string | null // API input for birthDate is string or null
}

export default class ProfileService {
  /**
   * Create or update a profile for a user
   */
  async createOrUpdate(
    userId: string,
    data: {
      firstName: string
      lastName: string
      birthDate?: string | null
      profilePicture?: string | null
      countryCode?: string | null
    }
  ) {
    // Find user to ensure they exist
    const targetUser = await User.find(userId)
    if (!targetUser) {
      throw new Error('User not found')
    }

    // Format birth date if provided
    const birthDate = data.birthDate ? DateTime.fromISO(data.birthDate) : null

    // Use Lucid's updateOrCreate method
    const profile = await Profile.updateOrCreate(
      { userId }, // Search payload (unique identifier to check if profile exists)
      {
        firstName: data.firstName,
        lastName: data.lastName,
        birthDate,
        profilePicture: data.profilePicture,
        countryCode: data.countryCode,
      }
    )

    await profile.load('settings')

    return profile
  }

  /**
   * Get a user's profile by their user ID
   */
  async getProfileByUserId(userId: string) {
    const profile = await Profile.query()
      .where('userId', userId)
      .whereNull('deletedAt')
      .preload('settings')
      .first()

    return profile
  }

  async deleteProfile(userId: string) {
    const profile = await Profile.findByOrFail('userId', userId)
    await profile.delete()
  }

  /**
   * Partially update a user's profile by their user ID
   */
  async updatePartial(
    userId: string,
    data: ProfilePatchData // Use the ProfilePatchData interface
  ) {
    const profile = await Profile.findByOrFail('userId', userId)

    // Prepare the payload, handling birthDate conversion
    const payloadForMerge: ProfileForMerge = {} // Use the ProfileForMerge interface

    if (data.firstName !== undefined) {
      payloadForMerge.firstName = data.firstName
    }
    if (data.lastName !== undefined) {
      payloadForMerge.lastName = data.lastName
    }
    if (data.profilePicture !== undefined) {
      payloadForMerge.profilePicture = data.profilePicture
    }
    if (data.countryCode !== undefined) {
      payloadForMerge.countryCode = data.countryCode
    }

    if (data.birthDate !== undefined) {
      // data.birthDate is string | null as per ProfilePatchData
      payloadForMerge.birthDate = data.birthDate ? DateTime.fromISO(data.birthDate) : null
    }

    profile.merge(payloadForMerge)
    await profile.save()

    // Preload the settings relationship to include ProfileSettings in the response
    await profile.load('settings')

    return profile
  }
}
