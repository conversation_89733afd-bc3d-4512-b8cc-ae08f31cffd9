import type { HttpContext } from '@adonisjs/core/http'
import ProfileSettings from './profile_settings_model.js'
import { notFoundException } from '#utils/error'
import {
  createProfileSettingsValidator,
  updateProfileSettingsValidator,
} from './profile_settings_validator.js'

export default class ProfileSettingsController {
  private baseController: any = null

  /**
   * Initialize base controller dynamically for HMR compatibility
   */
  private async initBaseController() {
    if (!this.baseController) {
      const baseControllerModule = await import('#controllers/base_controller')
      this.baseController = baseControllerModule
    }
    return this.baseController
  }

  /**
   * Configuration for base controller utilities
   */
  private async getConfig() {
    await this.initBaseController()

    // Adapter wrapping ProfileSettings model operations to conform to BaseResourceService interface
    const profileSettingsServiceAdapter = {
      getResourceById: async (id: string): Promise<any> => {
        const profileSettings = await ProfileSettings.query()
          .where('id', id)
          .preload('profile')
          .first()
        return profileSettings
          ? { ...profileSettings.serialize(), userId: profileSettings.profile?.userId }
          : null
      },

      getResources: async (userId: string): Promise<any> => {
        const { default: Profile } = await import('./profile_model.js')
        const profile = await Profile.query().where('userId', userId).preload('settings').first()

        const settings = profile?.settings
        return {
          data: settings ? [{ ...settings.serialize(), userId: userId }] : [],
          pagination: undefined,
        }
      },

      deleteResource: async (id: string, userId: string): Promise<boolean> => {
        const profileSettings = await ProfileSettings.query()
          .where('id', id)
          .preload('profile', (profileQuery) => {
            profileQuery.where('userId', userId)
          })
          .first()

        if (!profileSettings || !profileSettings.profile) {
          throw notFoundException('Profile settings not found or access denied')
        }

        await profileSettings.delete()
        return true
      },

      createResource: async (userId: string, data: any): Promise<any> => {
        // Verify the profile belongs to the authenticated user
        const { default: Profile } = await import('./profile_model.js')
        const profile = await Profile.query()
          .where('id', data.profileId)
          .where('userId', userId)
          .first()

        if (!profile) {
          throw notFoundException('Profile not found or access denied')
        }

        // Validate privacy template ownership if provided
        if (data.defaultPolicyTemplateId) {
          const { default: PrivacyPolicyTemplate } = await import(
            '../privacy-policy/template/privacy_policy_template_model.js'
          )
          const template = await PrivacyPolicyTemplate.query()
            .where('id', data.defaultPolicyTemplateId)
            .where('userId', userId)
            .first()

          if (!template) {
            throw notFoundException('Privacy policy template not found or access denied')
          }
        }

        // Enforce one-to-one relationship: Check if settings already exist for this profile
        // Note: Using whereNull('deletedAt') to exclude soft-deleted records
        const existingSettings = await ProfileSettings.query()
          .where('profileId', data.profileId)
          .whereNull('deletedAt')
          .first()

        if (existingSettings) {
          // Return 422 to enforce uniqueness constraint (one-to-one relationship)
          const error = new Error('Profile settings already exist for this profile')
          ;(error as any).status = 422
          ;(error as any).code = 'E_VALIDATION_ERROR'
          ;(error as any).messages = [
            {
              message: 'Profile settings already exist for this profile',
              field: 'profileId',
              rule: 'unique',
            },
          ]
          throw error
        }

        // Apply explicit defaults for fields that should have default values
        const profileSettingsData = {
          ...data,
          defaultAutoAcceptInvitations: data.defaultAutoAcceptInvitations ?? false,
          globalBusyMessage: data.globalBusyMessage ?? null,
          defaultPolicyTemplateId: data.defaultPolicyTemplateId ?? null,
        }

        const profileSettings = await ProfileSettings.create(profileSettingsData)
        // Ensure null values are explicitly included in the response
        const serialized = profileSettings.serialize()
        return {
          ...serialized,
          userId: userId,
          globalBusyMessage: serialized.globalBusyMessage ?? null,
          defaultPolicyTemplateId: serialized.defaultPolicyTemplateId ?? null,
        }
      },

      updateResource: async (id: string, data: any): Promise<any> => {
        const profileSettings = await ProfileSettings.findOrFail(id)
        profileSettings.merge(data)
        await profileSettings.save()

        await profileSettings.load('profile')
        return { ...profileSettings.serialize(), userId: profileSettings.profile?.userId }
      },
    }

    return {
      service: profileSettingsServiceAdapter,
      resourceName: 'profile settings',
      validateQueryParams: async () => ({}), // Profile settings don't have complex query params
      // No custom status or sort mapping needed for profile settings
      validateCreatePayload: async (request: any) =>
        await request.validateUsing(createProfileSettingsValidator),
      validateUpdatePayload: async (request: any) =>
        await request.validateUsing(updateProfileSettingsValidator),
    }
  }

  /**
   * List profile settings for the authenticated user
   */
  index = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createIndexHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Get profile settings for the authenticated user's profile
   */
  show = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createShowHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Create profile settings for a specific profile
   */
  store = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createStoreHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Update profile settings
   */
  update = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createUpdateHandler(await this.getConfig())
    return handler(ctx)
  }

  /**
   * Delete profile settings
   */
  destroy = async (ctx: HttpContext) => {
    const base = await this.initBaseController()
    const handler = base.createDestroyHandler(await this.getConfig())
    return handler(ctx)
  }
}
